<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\Fetcher\Logics;

use App\Classes\ValueObjects\RawContentObject;
use App\Helpers\ContentHelper;
use App\Modules\AiModels\AiModelClient;
use App\Modules\Fetcher\Services\ExtractsRssItemsFromRawContent;
use App\Modules\Fetcher\Services\RetrievesArticleDataFromRawContent;
use App\Repositories\ArticleRepository;
use Mockery;
use Tests\TestCase;

class RetrievesArticleDataFromRawContentTest extends TestCase {
    public function testExecuteForRss(): void {
        $rawContent       = '<rss><channel><item><title>Test</title><link>http://test.com</link></item></channel></rss>';
        $rawContentObject = new RawContentObject($rawContent);

        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('isContentRss')
            ->once()
            ->with($rawContent)
            ->andReturn(true);
        $extractsRssItemsFromRawContentMock = Mockery::mock(ExtractsRssItemsFromRawContent::class);
        $extractsRssItemsFromRawContentMock->shouldReceive('execute')
            ->once()
            ->with($rawContent)
            ->andReturn(['test']);

        $logic = new RetrievesArticleDataFromRawContent(
            $extractsRssItemsFromRawContentMock,
            Mockery::mock(ArticleRepository::class),
            Mockery::mock(AiModelClient::class),
            $contentHelperMock
        );
        $result = $logic->execute($rawContentObject);
        $this->assertEquals(['test'], $result);
    }

    public function testItExecuteForHtml(): void {
        $rawContent       = '<html><body><h1>Test</h1></body></html>';
        $rawContentObject = new RawContentObject($rawContent);

        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('isContentRss')
            ->once()
            ->with($rawContent)
            ->andReturn(false);

        $contentHelperMock->shouldReceive('getBodyFromHtmlContent')
            ->once()
            ->with($rawContent)
            ->andReturn('<body><h1>Test</h1></body>');

        $aiModelClientMock = Mockery::mock(AiModelClient::class);
        $aiModelClientMock->shouldReceive('ask')
            ->once()
            ->andReturn('["http://test.com"]');

        $contentHelperMock->shouldReceive('getCanonicalUrl')
            ->once()
            ->with('http://test.com')
            ->andReturn('http://test.com');
        $articleRepositoryMock = Mockery::mock(ArticleRepository::class);
        $articleRepositoryMock->shouldReceive('findWhere')
            ->once()
            ->andReturn(null);

        $logic = new RetrievesArticleDataFromRawContent(
            Mockery::mock(ExtractsRssItemsFromRawContent::class),
            $articleRepositoryMock,
            $aiModelClientMock,
            $contentHelperMock
        );
        $result = $logic->execute($rawContentObject);
        $this->assertEquals(['http://test.com'], $result);
    }

    public function testItExtractReturnsEmptyWhenNoBracketsFound(): void {
        $rawContent       = '<html><body><h1>Test</h1></body></html>';
        $rawContentObject = new RawContentObject($rawContent);

        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('isContentRss')
            ->once()
            ->with($rawContent)
            ->andReturn(false);

        $contentHelperMock->shouldReceive('getBodyFromHtmlContent')
            ->once()
            ->with($rawContent)
            ->andReturn('<body><h1>Test</h1></body>');

        $aiModelClientMock = Mockery::mock(AiModelClient::class);
        $aiModelClientMock->shouldReceive('ask')
            ->once()
            ->andReturn('Sorry, no links found');

        $logic = new RetrievesArticleDataFromRawContent(
            Mockery::mock(ExtractsRssItemsFromRawContent::class),
            Mockery::mock(ArticleRepository::class),
            $aiModelClientMock,
            $contentHelperMock
        );

        $result = $logic->execute($rawContentObject);
        $this->assertEquals([], $result);
    }

    public function testItExtractReturnsEmptyWhenNoBodyFound(): void {
        $rawContent       = '<html><h1>Test</h1></html>';
        $rawContentObject = new RawContentObject($rawContent);

        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('isContentRss')
            ->once()
            ->with($rawContent)
            ->andReturn(false);

        $contentHelperMock->shouldReceive('getBodyFromHtmlContent')
            ->once()
            ->with($rawContent)
            ->andReturn('');

        $logic = new RetrievesArticleDataFromRawContent(
            Mockery::mock(ExtractsRssItemsFromRawContent::class),
            Mockery::mock(ArticleRepository::class),
            Mockery::mock(AiModelClient::class),
            $contentHelperMock
        );

        $result = $logic->execute($rawContentObject);
        $this->assertEquals([], $result);
    }
}
