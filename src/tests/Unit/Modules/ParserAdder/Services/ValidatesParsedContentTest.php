<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\ParserAdder\Services;

use App\Classes\Constants\Parser;
use App\Exceptions\ServiceException;
use App\Helpers\ContentHelper;
use App\Helpers\MediaHelper;
use App\Modules\ParserAdder\Services\ValidatesParsedContent;
use App\Repositories\PublisherRepository;
use Carbon\Carbon;
use Mockery;
use Tests\TestCase;

class ValidatesParsedContentTest extends TestCase {
    public function testItValidatesParsedContent(): void {

        $parsedContent = [
            'article_id'      => $this->generator->numberBetween(1, 1000),
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => Carbon::parse($this->generator->dateTime())->toDateTimeString(),
            'modified_date'  => Carbon::parse($this->generator->dateTime())->toDateTimeString(),
        ];

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $service          = $this->createValidatesParsedContentService($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        $this->assertEquals($parsedContent, $validatedContent);
    }

    public function testItValidatesParsedContentWithoutDescription(): void {
        $parsedContent = [
            'article_id'      => $this->generator->numberBetween(1, 1000),
            'title'           => $this->generator->title(),
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];

        $expectedDescription = mb_substr(strip_tags($parsedContent['full_content']), 0, Parser::DESCRIPTION_LENGTH) . '...';

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $service          = $this->createValidatesParsedContentService($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        $this->assertEquals($expectedDescription, $validatedContent['description']);
    }

    public function testItValidatesParsedContentWithLongDescription(): void {
        $longDescription = str_repeat('This is a very long description that should be truncated. ', 10);

        $parsedContent = [
            'article_id'      => $this->generator->numberBetween(1, 1000),
            'title'           => $this->generator->title(),
            'description'     => $longDescription,
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];

        $expectedDescription = mb_substr($longDescription, 0, Parser::DESCRIPTION_LENGTH);

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $service          = $this->createValidatesParsedContentService($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        $this->assertEquals($expectedDescription, $validatedContent['description']);
        $this->assertEquals(Parser::DESCRIPTION_LENGTH, mb_strlen($validatedContent['description']));
    }

    public function testItValidatesParsedContentWithoutAuthor(): void {
        $parsedContent = [
            'article_id'      => $this->generator->numberBetween(1, 1000),
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $publisherRepositoryMock->shouldReceive('findWhere')
            ->once()
            ->andReturn($mockPublisher);

        $service          = $this->createValidatesParsedContentService($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        $this->assertEquals($mockPublisher->name, $validatedContent['author']);
    }

    public function testItValidatesParsedContentWithoutCoverImage(): void {

        $mockMedia = [
            'url'     => $this->generator->url(),
            'caption' => $this->generator->sentence(),
        ];

        $parsedContent = [
            'article_id'      => $this->generator->numberBetween(1, 1000),
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph() . '<img src="' . $mockMedia['url'] . '" alt="' . $mockMedia['caption'] . '">',
            'link'            => $this->generator->url(),
            'media'           => [],
            'author'          => $this->generator->name(),
            'published_date'  => $this->generator->date(),
            'modified_date'   => $this->generator->date(),
        ];

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $mediaHelperMock->shouldReceive('isImageUrl')
            ->twice()
            ->andReturn(true);

        $service          = $this->createValidatesParsedContentService($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        $this->assertEquals(['url' => $mockMedia['url'], 'caption' => $mockMedia['caption']], $validatedContent['cover_image_url']);
    }

    public function testItValidatesParsedContentWithoutCoverImageAndMedia(): void {
        $parsedContent = [
            'article_id'      => $this->generator->numberBetween(1, 1000),
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'media'           => [],
            'author'          => $this->generator->name(),
            'published_date'  => $this->generator->date(),
            'modified_date'   => $this->generator->date(),
        ];

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $service          = $this->createValidatesParsedContentService($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        $this->assertEquals(['url' => '', 'caption' => null], $validatedContent['cover_image_url']);
    }

    public function testItValidatesParsedContentWithEmptyMediaUrl(): void {
        $parsedContent = [
            'article_id'      => $this->generator->numberBetween(1, 1000),
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'media'           => [
                [
                    'url'     => '',
                    'caption' => $this->generator->sentence(),
                ],
            ],
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => null,
            ],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $service          = $this->createValidatesParsedContentService($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        $this->assertEquals([], $validatedContent['media']);
    }

    public function testItMissingRequiredField(): void {
        $parsedContent           = [];
        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $service = $this->createValidatesParsedContentService($publisherRepositoryMock, $mediaHelperMock);

        $this->expectException(ServiceException::class);
        $service->execute($parsedContent, $mockPublisher->id);
    }

    public function testItValidatesParsedContentWithVideoMedia(): void {
        $mockVideoUrl = 'https://example.com/video.mp4';

        $parsedContent = [
            'article_id'      => $this->generator->numberBetween(1, 1000),
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph() . '<video src="' . $mockVideoUrl . '" controls></video>',
            'link'            => $this->generator->url(),
            'media'           => [],
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $mediaHelperMock->shouldReceive('isVideoUrl')
            ->once()
            ->with($mockVideoUrl)
            ->andReturn(true);

        $service          = $this->createValidatesParsedContentService($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        $expectedMedia = [
            [
                'url'     => $mockVideoUrl,
                'caption' => null,
            ],
        ];

        $this->assertEquals($expectedMedia, $validatedContent['media']);
    }

    public function testItValidatesParsedContentWithMultipleVideoMedia(): void {
        $mockVideoUrl1 = 'https://example.com/video1.mp4';
        $mockVideoUrl2 = 'https://example.com/video2.mov';

        $parsedContent = [
            'article_id'      => $this->generator->numberBetween(1, 1000),
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph() .
                               '<video src="' . $mockVideoUrl1 . '" controls></video>' .
                               '<video src="' . $mockVideoUrl2 . '" controls></video>',
            'link'            => $this->generator->url(),
            'media'           => [],
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $mediaHelperMock->shouldReceive('isVideoUrl')
            ->twice()
            ->andReturn(true);

        $service          = $this->createValidatesParsedContentService($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        $expectedMedia = [
            [
                'url'     => $mockVideoUrl1,
                'caption' => null,
            ],
            [
                'url'     => $mockVideoUrl2,
                'caption' => null,
            ],
        ];

        $this->assertEquals($expectedMedia, $validatedContent['media']);
    }

    public function testItValidatesParsedContentWithMixedImageAndVideoMedia(): void {
        $mockImageUrl     = 'https://example.com/image.jpg';
        $mockVideoUrl     = 'https://example.com/video.mp4';
        $mockImageCaption = $this->generator->sentence();

        $parsedContent = [
            'article_id'      => $this->generator->numberBetween(1, 1000),
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph() .
                               '<img src="' . $mockImageUrl . '" alt="' . $mockImageCaption . '">' .
                               '<video src="' . $mockVideoUrl . '" controls></video>',
            'link'            => $this->generator->url(),
            'media'           => [],
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $mediaHelperMock->shouldReceive('isImageUrl')
            ->once()
            ->with($mockImageUrl)
            ->andReturn(true);

        $mediaHelperMock->shouldReceive('isVideoUrl')
            ->once()
            ->with($mockVideoUrl)
            ->andReturn(true);

        $service          = $this->createValidatesParsedContentService($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        $expectedMedia = [
            [
                'url'     => $mockImageUrl,
                'caption' => $mockImageCaption,
            ],
            [
                'url'     => $mockVideoUrl,
                'caption' => null,
            ],
        ];

        $this->assertEquals($expectedMedia, $validatedContent['media']);
    }

    public function testItExtractsImageCaptionFromAltAttribute(): void {
        $mockImageUrl     = 'https://example.com/image.jpg';
        $mockImageCaption = $this->generator->sentence();

        $parsedContent = [
            'article_id'      => $this->generator->numberBetween(1, 1000),
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph() . '<img src="' . $mockImageUrl . '" alt="' . $mockImageCaption . '">',
            'link'            => $this->generator->url(),
            'media'           => [],
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $mediaHelperMock->shouldReceive('isImageUrl')
            ->once()
            ->with($mockImageUrl)
            ->andReturn(true);

        $service          = $this->createValidatesParsedContentService($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        $expectedMedia = [
            [
                'url'     => $mockImageUrl,
                'caption' => $mockImageCaption,
            ],
        ];

        $this->assertEquals($expectedMedia, $validatedContent['media']);
    }

    public function testItExtractsImageCaptionFromTitleAttribute(): void {
        $mockImageUrl     = 'https://example.com/image.jpg';
        $mockImageCaption = $this->generator->sentence();

        $parsedContent = [
            'article_id'      => $this->generator->numberBetween(1, 1000),
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph() . '<img src="' . $mockImageUrl . '" title="' . $mockImageCaption . '">',
            'link'            => $this->generator->url(),
            'media'           => [],
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $mediaHelperMock->shouldReceive('isImageUrl')
            ->once()
            ->with($mockImageUrl)
            ->andReturn(true);

        $service          = $this->createValidatesParsedContentService($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        $expectedMedia = [
            [
                'url'     => $mockImageUrl,
                'caption' => $mockImageCaption,
            ],
        ];

        $this->assertEquals($expectedMedia, $validatedContent['media']);
    }

    public function testItExtractsImageCaptionFromFigcaption(): void {
        $mockImageUrl     = 'https://example.com/image.jpg';
        $mockImageCaption = $this->generator->sentence();

        $parsedContent = [
            'article_id'      => $this->generator->numberBetween(1, 1000),
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph() .
                               '<figure><img src="' . $mockImageUrl . '"><figcaption>' . $mockImageCaption . '</figcaption></figure>',
            'link'            => $this->generator->url(),
            'media'           => [],
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $mediaHelperMock->shouldReceive('isImageUrl')
            ->once()
            ->with($mockImageUrl)
            ->andReturn(true);

        $service          = $this->createValidatesParsedContentService($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        $expectedMedia = [
            [
                'url'     => $mockImageUrl,
                'caption' => $mockImageCaption,
            ],
        ];

        $this->assertEquals($expectedMedia, $validatedContent['media']);
    }

    public function testItExtractsImageCaptionPrioritizesAltOverTitle(): void {
        $mockImageUrl = 'https://example.com/image.jpg';
        $altCaption   = 'Alt caption';
        $titleCaption = 'Title caption';

        $parsedContent = [
            'article_id'      => $this->generator->numberBetween(1, 1000),
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph() . '<img src="' . $mockImageUrl . '" alt="' . $altCaption . '" title="' . $titleCaption . '">',
            'link'            => $this->generator->url(),
            'media'           => [],
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $mediaHelperMock->shouldReceive('isImageUrl')
            ->once()
            ->with($mockImageUrl)
            ->andReturn(true);

        $service          = $this->createValidatesParsedContentService($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        $expectedMedia = [
            [
                'url'     => $mockImageUrl,
                'caption' => $altCaption, // Should prioritize alt over title
            ],
        ];

        $this->assertEquals($expectedMedia, $validatedContent['media']);
    }

    public function testItHandlesImageWithoutCaption(): void {
        $mockImageUrl = 'https://example.com/image.jpg';

        $parsedContent = [
            'article_id'      => $this->generator->numberBetween(1, 1000),
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph() . '<img src="' . $mockImageUrl . '">',
            'link'            => $this->generator->url(),
            'media'           => [],
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => null,
            ],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $mediaHelperMock->shouldReceive('isImageUrl')
            ->andReturn(true);

        $service          = $this->createValidatesParsedContentService($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        $this->assertEquals(['url' => $parsedContent['cover_image_url']['url'], 'caption' => $parsedContent['cover_image_url']['caption']], $validatedContent['cover_image_url']);
    }

    public function testItNoPublishedDateAndModifiedDate(): void {
        $parsedContent = [
            'article_id'      => $this->generator->numberBetween(1, 1000),
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => null,
            'modified_date'  => null,
        ];

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $service          = $this->createValidatesParsedContentService($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        // Should fallback to current datetime format when parsing fails
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $validatedContent['published_date']);
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $validatedContent['modified_date']);
    }

    public function testItValidatesIso8601DateTimeFormat(): void {
        $parsedContent = [
            'article_id'      => $this->generator->numberBetween(1, 1000),
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => '2025-06-20T06:45:13.000Z', // ISO 8601 format
            'modified_date'  => '2025-07-07T03:30:00Z',     // ISO 8601 format
        ];

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $service          = $this->createValidatesParsedContentService($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        // Should be converted to MySQL datetime format
        $this->assertEquals('2025-06-20 06:45:13', $validatedContent['published_date']);
        $this->assertEquals('2025-07-07 03:30:00', $validatedContent['modified_date']);
    }

    public function testItHandlesInvalidDateTimeFormat(): void {
        $parsedContent = [
            'article_id'      => $this->generator->numberBetween(1, 1000),
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => 'invalid-date-format',
            'modified_date'  => 'another-invalid-date',
        ];

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $service          = $this->createValidatesParsedContentService($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        // Should fallback to current datetime format when parsing fails
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $validatedContent['published_date']);
        $this->assertMatchesRegularExpression('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/', $validatedContent['modified_date']);
    }

    public function testItValidatesLargeArticleId(): void {
        $largeArticleId = 7869415894; // Exceeds MySQL INT max value (2,147,483,647)

        $parsedContent = [
            'article_id'      => $largeArticleId,
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $service          = $this->createValidatesParsedContentService($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        $validatedArticleId = $validatedContent['article_id'];
        $this->assertLessThanOrEqual(2147483647, $validatedArticleId);
        $this->assertGreaterThanOrEqual(0, $validatedArticleId);
        $this->assertNotEquals($largeArticleId, $validatedArticleId);
    }

    public function testItValidatesNormalArticleId(): void {
        $normalArticleId = 12345; // Within MySQL INT range

        $parsedContent = [
            'article_id'      => $normalArticleId,
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $service          = $this->createValidatesParsedContentService($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        // Should remain unchanged for normal values
        $this->assertEquals($normalArticleId, $validatedContent['article_id']);
    }

    public function testItValidatesLongTitle(): void {
        $longTitle = str_repeat('This is a very long title that should be truncated. ', 10); // Over 255 chars

        $parsedContent = [
            'article_id'      => $this->generator->numberBetween(1, 1000),
            'title'           => $longTitle,
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $service          = $this->createValidatesParsedContentService($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        $this->assertLessThanOrEqual(Parser::TITLE_MAX_LENGTH, mb_strlen($validatedContent['title']));
        $this->assertEquals(Parser::TITLE_MAX_LENGTH, mb_strlen($validatedContent['title']));
    }

    public function testItValidatesLongAuthor(): void {
        $longAuthor = str_repeat('Very Long Author Name ', 10); // Over 100 chars

        $parsedContent = [
            'article_id'      => $this->generator->numberBetween(1, 1000),
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $longAuthor,
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $service          = $this->createValidatesParsedContentService($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        $this->assertLessThanOrEqual(Parser::AUTHOR_MAX_LENGTH, mb_strlen($validatedContent['author']));
        $this->assertEquals(Parser::AUTHOR_MAX_LENGTH, mb_strlen($validatedContent['author']));
    }

    public function testItValidatesLongDescription(): void {
        $longDescription = str_repeat('This is a very long description that should be truncated. ', 10); // Over 350 chars

        $parsedContent = [
            'article_id'      => $this->generator->numberBetween(1, 1000),
            'title'           => $this->generator->title(),
            'description'     => $longDescription,
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $service          = $this->createValidatesParsedContentService($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        $this->assertLessThanOrEqual(Parser::DESCRIPTION_LENGTH, mb_strlen($validatedContent['description']));
        $this->assertEquals(Parser::DESCRIPTION_LENGTH, mb_strlen($validatedContent['description']));
    }

    public function testItValidatesFullContentWithoutTruncation(): void {
        $longContent = str_repeat('This is a very long content that should not be truncated. ', 2000); // ~120,000 characters

        $parsedContent = [
            'article_id'      => $this->generator->numberBetween(1, 1000),
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $longContent,
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $service          = $this->createValidatesParsedContentService($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        $this->assertEquals($longContent, $validatedContent['full_content']);
        $this->assertEquals(mb_strlen($longContent), mb_strlen($validatedContent['full_content']));
    }

    public function testItValidatesFullContentWithHtmlEntities(): void {
        $contentWithEntities = 'This is content with &amp; &lt; &gt; &quot; entities';
        $expectedContent     = 'This is content with & < > " entities';

        $parsedContent = [
            'article_id'      => $this->generator->numberBetween(1, 1000),
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $contentWithEntities,
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $service          = $this->createValidatesParsedContentService($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        $this->assertEquals($expectedContent, $validatedContent['full_content']);
    }

    public function testItDecodesHtmlEntitiesInTitleAndDescription(): void {
        $parsedContent = [
            'article_id'      => 1,
            'title'           => 'Test &amp; Example &quot;Title&quot;',
            'description'     => 'Test &lt;description&gt; with &amp; entities',
            'full_content'    => '<p>Test content with &amp; entities</p>',
            'link'            => 'https://example.com',
            'cover_image_url' => ['url' => '', 'caption' => null],
            'media'           => [],
            'author'          => 'Test Author',
            'published_date'  => '2025-01-01 00:00:00',
            'modified_date'   => '2025-01-01 00:00:00',
        ];

        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = Mockery::mock(MediaHelper::class);

        $service          = $this->createValidatesParsedContentService($publisherRepositoryMock, $mediaHelperMock);
        $validatedContent = $service->execute($parsedContent, $mockPublisher->id);

        $this->assertEquals('Test & Example "Title"', $validatedContent['title']);
        $this->assertEquals('Test  with & entities', $validatedContent['description']); // strip_tags removes <description>
        $this->assertEquals('<p>Test content with & entities</p>', $validatedContent['full_content']);
    }

    private function createValidatesParsedContentService($publisherRepositoryMock = null, $mediaHelperMock = null): ValidatesParsedContent {
        $publisherRepositoryMock = $publisherRepositoryMock ?: Mockery::mock(PublisherRepository::class);
        $mediaHelperMock         = $mediaHelperMock ?: Mockery::mock(MediaHelper::class);
        $contentHelperMock       = Mockery::mock(ContentHelper::class);

        // Set up default behavior for ContentHelper
        $contentHelperMock->shouldReceive('decodeHtmlEntities')
            ->andReturnUsing(function ($content) {
                return html_entity_decode($content, ENT_QUOTES | ENT_HTML5, 'UTF-8');
            });

        return new ValidatesParsedContent($publisherRepositoryMock, $mediaHelperMock, $contentHelperMock);
    }
}
