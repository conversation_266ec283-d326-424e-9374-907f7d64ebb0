<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\ParserAdder\Services;

use App\Helpers\ContentHelper;
use App\Modules\AiModels\AiModelClient;
use App\Modules\ParserAdder\Services\ExtractsContentArrayFromAiResponse;
use App\Modules\ParserAdder\Services\ParsesArticleContent;
use App\Modules\ParserAdder\Services\ParsesArticleWithDefaultParser;
use App\Modules\ParserAdder\Services\SanitizesParsedContent;
use App\Modules\ParserAdder\Services\ValidatesParsedContent;
use Mockery;
use Tests\TestCase;
use Tests\Traits\LoadsStubs;

class ParsesArticleContentTest extends TestCase {
    use LoadsStubs;

    public function testItParsesArticleContentWithAiParsingAndIsHtmlContent(): void {
        $rawContent         = $this->getFullHtmlFetchedStub();
        $customPrompt       = '';
        $useAiParsing       = true;
        $isFullHtmlContent  = true;
        $publisherId        = 1;

        $mockData = [
            'title'           => 'Test Article',
            'description'     => 'Test Description',
            'full_content'    => 'Test Content',
            'link'            => 'https://example.com/article',
            'cover_image_url' => ['url' => 'https://example.com/image.jpg', 'caption' => null],
            'author'          => 'Test Author',
            'published_date'  => '2024-01-01 00:00:00',
            'modified_date'   => '2024-01-01 00:00:00',
        ];

        $mockExtractsContentArrayFromAiResponse = Mockery::mock(ExtractsContentArrayFromAiResponse::class);
        $mockExtractsContentArrayFromAiResponse->shouldReceive('execute')->once()->andReturn($mockData);

        $mockAiModelClient = Mockery::mock(AiModelClient::class);
        $mockAiModelClient->shouldReceive('ask')->once()->andReturn($this->getParsedDataStub());

        $mockValidatesParsedContent = Mockery::mock(ValidatesParsedContent::class);
        $mockValidatesParsedContent->shouldReceive('execute')->once()->andReturn($mockData);

        $mockSanitizesParsedContent = Mockery::mock(SanitizesParsedContent::class);
        $mockSanitizesParsedContent->shouldReceive('execute')->once()->andReturn($mockData);

        $service = new ParsesArticleContent(
            $mockExtractsContentArrayFromAiResponse,
            Mockery::mock(ParsesArticleWithDefaultParser::class),
            $mockValidatesParsedContent,
            $mockSanitizesParsedContent,
            $mockAiModelClient,
            Mockery::mock(ContentHelper::class)
        );

        $result = $service->execute($rawContent, $customPrompt, $useAiParsing, $isFullHtmlContent, $publisherId);
        $this->assertIsArray($result);
    }

    public function testItParsesArticleContentWithAiParsingAndIsNotHtmlContent(): void {
        $rawContent         = $this->getRssItemStubWithGuid();
        $customPrompt       = '';
        $useAiParsing       = true;
        $isFullHtmlContent  = false;
        $publisherId        = 1;

        $mockData = [
            'title'           => 'Test Article',
            'description'     => 'Test Description',
            'full_content'    => 'Test Content',
            'link'            => 'https://example.com/article',
            'cover_image_url' => ['url' => 'https://example.com/image.jpg', 'caption' => null],
            'author'          => 'Test Author',
            'published_date'  => '2024-01-01 00:00:00',
            'modified_date'   => '2024-01-01 00:00:00',
        ];

        $mockExtractsContentArrayFromAiResponse = Mockery::mock(ExtractsContentArrayFromAiResponse::class);
        $mockExtractsContentArrayFromAiResponse->shouldReceive('execute')->once()->andReturn($mockData);

        $mockAiModelClient = Mockery::mock(AiModelClient::class);
        $mockAiModelClient->shouldReceive('ask')->once()->andReturn($this->getParsedDataStub());

        $mockContentHelper = Mockery::mock(ContentHelper::class);
        $mockContentHelper->shouldReceive('getContentFromRawRssItem')->once()->andReturn([
            'full_content'     => 'Test Content',
            'filtered_content' => 'Test Content',
        ]);

        $mockValidatesParsedContent = Mockery::mock(ValidatesParsedContent::class);
        $mockValidatesParsedContent->shouldReceive('execute')->once()->andReturn($mockData);

        $mockSanitizesParsedContent = Mockery::mock(SanitizesParsedContent::class);
        $mockSanitizesParsedContent->shouldReceive('execute')->once()->andReturn($mockData);

        $service = new ParsesArticleContent(
            $mockExtractsContentArrayFromAiResponse,
            Mockery::mock(ParsesArticleWithDefaultParser::class),
            $mockValidatesParsedContent,
            $mockSanitizesParsedContent,
            $mockAiModelClient,
            $mockContentHelper
        );

        $result = $service->execute($rawContent, $customPrompt, $useAiParsing, $isFullHtmlContent, $publisherId);
        $this->assertIsArray($result);
    }

    public function testItParsesArticleContentWithDefaultParser(): void {
        $rawContent         = $this->getRssItemStubWithGuid();
        $customPrompt       = '';
        $useAiParsing       = false;
        $isFullHtmlContent  = false;
        $publisherId        = 1;

        $mockData = [
            'title'           => 'Test Article',
            'description'     => 'Test Description',
            'full_content'    => 'Test Content',
            'link'            => 'https://example.com/article',
            'cover_image_url' => ['url' => 'https://example.com/image.jpg', 'caption' => null],
            'author'          => 'Test Author',
            'published_date'  => '2024-01-01 00:00:00',
            'modified_date'   => '2024-01-01 00:00:00',
        ];

        $mockParsesArticleWithDefaultParser = Mockery::mock(ParsesArticleWithDefaultParser::class);
        $mockParsesArticleWithDefaultParser->shouldReceive('execute')->once()->andReturn($mockData);

        $mockValidatesParsedContent = Mockery::mock(ValidatesParsedContent::class);
        $mockValidatesParsedContent->shouldReceive('execute')->once()->andReturn($mockData);

        $mockSanitizesParsedContent = Mockery::mock(SanitizesParsedContent::class);
        $mockSanitizesParsedContent->shouldReceive('execute')->once()->andReturn($mockData);

        $service = new ParsesArticleContent(
            Mockery::mock(ExtractsContentArrayFromAiResponse::class),
            $mockParsesArticleWithDefaultParser,
            $mockValidatesParsedContent,
            $mockSanitizesParsedContent,
            Mockery::mock(AiModelClient::class),
            Mockery::mock(ContentHelper::class)
        );

        $result = $service->execute($rawContent, $customPrompt, $useAiParsing, $isFullHtmlContent, $publisherId);
        $this->assertIsArray($result);
    }

    public function testItParsesArticleContentWithDefaultParserAndRetriesWithAi(): void {
        $rawContent         = $this->getRssItemStubWithGuid();
        $customPrompt       = '';
        $useAiParsing       = false;
        $isFullHtmlContent  = false;
        $publisherId        = 1;

        $incompleteData = [
            'title'           => '',
            'description'     => '',
            'full_content'    => 'Test Content',
            'link'            => 'https://example.com/article',
            'cover_image_url' => ['url' => '', 'caption' => null],
            'author'          => '',
            'published_date'  => '',
            'modified_date'   => '',
        ];

        $completeData = [
            'title'           => 'Test Article',
            'description'     => 'Test Description',
            'full_content'    => 'Test Content',
            'link'            => 'https://example.com/article',
            'cover_image_url' => ['url' => 'https://example.com/image.jpg', 'caption' => null],
            'author'          => 'Test Author',
            'published_date'  => '2024-01-01 00:00:00',
            'modified_date'   => '2024-01-01 00:00:00',
        ];

        $mockParsesArticleWithDefaultParser = Mockery::mock(ParsesArticleWithDefaultParser::class);
        $mockParsesArticleWithDefaultParser->shouldReceive('execute')->once()->andReturn($incompleteData);

        $mockExtractsContentArrayFromAiResponse = Mockery::mock(ExtractsContentArrayFromAiResponse::class);
        $mockExtractsContentArrayFromAiResponse->shouldReceive('execute')->once()->andReturn($completeData);

        $mockContentHelper = Mockery::mock(ContentHelper::class);
        $mockContentHelper->shouldReceive('getContentFromRawRssItem')->once()->andReturn([
            'full_content'     => 'Test Content',
            'filtered_content' => 'Test Content',
        ]);

        $mockAiModelClient = Mockery::mock(AiModelClient::class);
        $mockAiModelClient->shouldReceive('ask')->once()->andReturn($this->getParsedDataStub());

        $mockValidatesParsedContent = Mockery::mock(ValidatesParsedContent::class);
        $mockValidatesParsedContent->shouldReceive('execute')->once()->andReturn($completeData);

        $mockSanitizesParsedContent = Mockery::mock(SanitizesParsedContent::class);
        $mockSanitizesParsedContent->shouldReceive('execute')->once()->andReturn($completeData);

        $service = new ParsesArticleContent(
            $mockExtractsContentArrayFromAiResponse,
            $mockParsesArticleWithDefaultParser,
            $mockValidatesParsedContent,
            $mockSanitizesParsedContent,
            $mockAiModelClient,
            $mockContentHelper
        );

        $result = $service->execute($rawContent, $customPrompt, $useAiParsing, $isFullHtmlContent, $publisherId);
        $this->assertIsArray($result);
    }

    public function testItReturnsEmptyArrayWhenAiReturnsEmptyArray(): void {
        $rawContent         = $this->getRssItemStubWithGuid();
        $customPrompt       = '';
        $useAiParsing       = true;
        $isFullHtmlContent  = false;
        $publisherId        = 1;

        $mockExtractsContentArrayFromAiResponse = Mockery::mock(ExtractsContentArrayFromAiResponse::class);
        $mockExtractsContentArrayFromAiResponse->shouldReceive('execute')->once()->andReturn([]);

        $mockAiModelClient = Mockery::mock(AiModelClient::class);
        $mockAiModelClient->shouldReceive('ask')->once()->andReturn($this->getParsedDataStub());

        $mockContentHelper = Mockery::mock(ContentHelper::class);
        $mockContentHelper->shouldReceive('getContentFromRawRssItem')->once()->andReturn([
            'full_content'     => 'Test Content',
            'filtered_content' => 'Test Content',
        ]);

        $service = new ParsesArticleContent(
            $mockExtractsContentArrayFromAiResponse,
            Mockery::mock(ParsesArticleWithDefaultParser::class),
            Mockery::mock(ValidatesParsedContent::class),
            Mockery::mock(SanitizesParsedContent::class),
            $mockAiModelClient,
            $mockContentHelper
        );

        $result = $service->execute($rawContent, $customPrompt, $useAiParsing, $isFullHtmlContent, $publisherId);
        $this->assertEquals([], $result);
    }

    public function testItParsesArticleContentWithCustomPrompt(): void {
        $rawContent         = $this->getFullHtmlFetchedStub();
        $customPrompt       = 'Extract article data with custom instructions';
        $useAiParsing       = true;
        $isFullHtmlContent  = true;
        $publisherId        = 1;

        $mockData = [
            'title'           => 'Test Article',
            'description'     => 'Test Description',
            'full_content'    => 'Test Content',
            'link'            => 'https://example.com/article',
            'cover_image_url' => ['url' => 'https://example.com/image.jpg', 'caption' => null],
            'author'          => 'Test Author',
            'published_date'  => '2024-01-01 00:00:00',
            'modified_date'   => '2024-01-01 00:00:00',
        ];

        $mockExtractsContentArrayFromAiResponse = Mockery::mock(ExtractsContentArrayFromAiResponse::class);
        $mockExtractsContentArrayFromAiResponse->shouldReceive('execute')->once()->andReturn($mockData);

        $mockAiModelClient = Mockery::mock(AiModelClient::class);
        $mockAiModelClient->shouldReceive('ask')
            ->once()
            ->with(
                Mockery::any(), // system prompt
                Mockery::pattern('/Extract article data with custom instructions/') // user prompt should contain custom prompt
            )
            ->andReturn($this->getParsedDataStub());

        $mockValidatesParsedContent = Mockery::mock(ValidatesParsedContent::class);
        $mockValidatesParsedContent->shouldReceive('execute')->once()->andReturn($mockData);

        $mockSanitizesParsedContent = Mockery::mock(SanitizesParsedContent::class);
        $mockSanitizesParsedContent->shouldReceive('execute')->once()->andReturn($mockData);

        $service = new ParsesArticleContent(
            $mockExtractsContentArrayFromAiResponse,
            Mockery::mock(ParsesArticleWithDefaultParser::class),
            $mockValidatesParsedContent,
            $mockSanitizesParsedContent,
            $mockAiModelClient,
            Mockery::mock(ContentHelper::class)
        );

        $result = $service->execute($rawContent, $customPrompt, $useAiParsing, $isFullHtmlContent, $publisherId);
        $this->assertIsArray($result);
    }

    public function testItSkipsProcessingWhenContentExceedsMaxLength(): void {
        $rawContent         = $this->getFullHtmlFetchedStub();
        $customPrompt       = '';
        $useAiParsing       = true;
        $isFullHtmlContent  = true;
        $publisherId        = 1;

        $excessiveContent = str_repeat('a', 70000); // Exceeds FULL_CONTENT_MAX_LENGTH (65000)

        $mockData = [
            'title'           => 'Test Article',
            'description'     => 'Test Description',
            'full_content'    => $excessiveContent,
            'link'            => 'https://example.com/article',
            'cover_image_url' => ['url' => 'https://example.com/image.jpg', 'caption' => null],
            'author'          => 'Test Author',
            'published_date'  => '2024-01-01 00:00:00',
            'modified_date'   => '2024-01-01 00:00:00',
        ];

        $mockExtractsContentArrayFromAiResponse = Mockery::mock(ExtractsContentArrayFromAiResponse::class);
        $mockExtractsContentArrayFromAiResponse->shouldReceive('execute')->once()->andReturn($mockData);

        $mockAiModelClient = Mockery::mock(AiModelClient::class);
        $mockAiModelClient->shouldReceive('ask')->once()->andReturn($this->getParsedDataStub());

        $mockValidatesParsedContent = Mockery::mock(ValidatesParsedContent::class);
        $mockValidatesParsedContent->shouldNotReceive('execute');

        $mockSanitizesParsedContent = Mockery::mock(SanitizesParsedContent::class);
        $mockSanitizesParsedContent->shouldNotReceive('execute');

        $service = new ParsesArticleContent(
            $mockExtractsContentArrayFromAiResponse,
            Mockery::mock(ParsesArticleWithDefaultParser::class),
            $mockValidatesParsedContent,
            $mockSanitizesParsedContent,
            $mockAiModelClient,
            Mockery::mock(ContentHelper::class)
        );

        $result = $service->execute($rawContent, $customPrompt, $useAiParsing, $isFullHtmlContent, $publisherId);
        $this->assertEmpty($result);
    }
}
