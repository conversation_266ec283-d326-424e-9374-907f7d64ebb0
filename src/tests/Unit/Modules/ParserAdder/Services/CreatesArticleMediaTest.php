<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\ParserAdder\Services;

use App\Helpers\MediaHelper;
use App\Models\Media;
use App\Models\Thumbnail;
use App\Modules\ParserAdder\Services\CreatesArticleMedia;
use App\Modules\ParserAdder\Services\RetrievesImageProxyLinks;
use App\Repositories\MediaRepository;
use App\Repositories\PublisherEndpointRepository;
use App\Repositories\ThumbnailRepository;
use Mockery;
use Tests\TestCase;

class CreatesArticleMediaTest extends TestCase {
    public function testItItCreatesArticleWithMedias(): void {
        $publisherId    = 123;
        $channelId      = 456;
        $mockCoverImage = [
            'url'     => $this->generator->url(),
            'caption' => $this->generator->sentence(),
        ];
        $mockMediaArray = [
            [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
        ];

        $mockMedia1     = Mockery::mock(Media::class);
        $mockMedia1->shouldReceive('getAttribute')->with('id')->andReturn(1);
        $mockMedia1->shouldReceive('getAttribute')->with('url')->andReturn($mockCoverImage['url']);

        $mockMedia2     = Mockery::mock(Media::class);
        $mockMedia2->shouldReceive('getAttribute')->with('id')->andReturn(2);
        $mockMedia2->shouldReceive('getAttribute')->with('url')->andReturn($mockMediaArray[0]['url']);

        $mockMedia3     = Mockery::mock(Media::class);
        $mockMedia3->shouldReceive('getAttribute')->with('id')->andReturn(3);
        $mockMedia3->shouldReceive('getAttribute')->with('url')->andReturn($mockMediaArray[1]['url']);

        $mockThumbnail1 = Mockery::mock(Thumbnail::class);
        $mockThumbnail2 = Mockery::mock(Thumbnail::class);
        $mockThumbnail3 = Mockery::mock(Thumbnail::class);

        $mediaRepositoryMock             = Mockery::mock(MediaRepository::class);
        $publisherEndpointRepositoryMock = Mockery::mock(PublisherEndpointRepository::class);
        $thumbnailRepositoryMock         = Mockery::mock(ThumbnailRepository::class);
        $retrievesImageProxyLinksMock    = Mockery::mock(RetrievesImageProxyLinks::class);
        $mediaHelperMock                 = Mockery::mock(MediaHelper::class);

        $publisherEndpointRepositoryMock
            ->shouldReceive('getHasProxyImage')
            ->once()
            ->with($publisherId, $channelId)
            ->andReturn(false);

        $mediaRepositoryMock->shouldReceive('findWhere')
            ->times(3)
            ->andReturn(null);

        $mediaHelperMock->shouldReceive('isMediaUrlValid')
            ->times(9)
            ->andReturn(true);

        $mediaHelperMock->shouldReceive('isVideoUrl')
            ->times(3)
            ->andReturn(false);

        $mediaHelperMock->shouldReceive('getImageSize')
            ->times(3)
            ->andReturn(['width' => 800, 'height' => 600]);

        $mediaRepositoryMock->shouldReceive('create')
            ->times(3)
            ->andReturn($mockMedia1, $mockMedia2, $mockMedia3);

        $thumbnailRepositoryMock->shouldReceive('create')
            ->times(3)
            ->andReturn($mockThumbnail1, $mockThumbnail2, $mockThumbnail3);

        $service = new CreatesArticleMedia(
            $mediaRepositoryMock,
            $publisherEndpointRepositoryMock,
            $thumbnailRepositoryMock,
            $retrievesImageProxyLinksMock,
            $mediaHelperMock
        );

        $mediaIds = $service->execute($mockCoverImage, $mockMediaArray, $publisherId, $channelId);

        $this->assertEquals([1, 2, 3], $mediaIds);
    }

    public function testItItCreatesArticleWithMediasAndProxyImage(): void {
        $publisherId    = 123;
        $channelId      = 456;
        $mockCoverImage = [
            'url'     => $this->generator->url(),
            'caption' => $this->generator->sentence(),
        ];
        $mockMediaArray = [
            [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
        ];

        $mockMedia1     = Mockery::mock(Media::class);
        $mockMedia1->shouldReceive('getAttribute')->with('id')->andReturn(1);
        $mockMedia1->shouldReceive('getAttribute')->with('url')->andReturn($mockCoverImage['url']);

        $mockMedia2     = Mockery::mock(Media::class);
        $mockMedia2->shouldReceive('getAttribute')->with('id')->andReturn(2);
        $mockMedia2->shouldReceive('getAttribute')->with('url')->andReturn($mockMediaArray[0]['url']);

        $mockMedia3     = Mockery::mock(Media::class);
        $mockMedia3->shouldReceive('getAttribute')->with('id')->andReturn(3);
        $mockMedia3->shouldReceive('getAttribute')->with('url')->andReturn($mockMediaArray[1]['url']);

        $mockThumbnail1 = Mockery::mock(Thumbnail::class);
        $mockThumbnail2 = Mockery::mock(Thumbnail::class);
        $mockThumbnail3 = Mockery::mock(Thumbnail::class);

        $mediaRepositoryMock             = Mockery::mock(MediaRepository::class);
        $publisherEndpointRepositoryMock = Mockery::mock(PublisherEndpointRepository::class);
        $thumbnailRepositoryMock         = Mockery::mock(ThumbnailRepository::class);
        $retrievesImageProxyLinksMock    = Mockery::mock(RetrievesImageProxyLinks::class);
        $mediaHelperMock                 = Mockery::mock(MediaHelper::class);

        $publisherEndpointRepositoryMock
            ->shouldReceive('getHasProxyImage')
            ->once()
            ->with($publisherId, $channelId)
            ->andReturn(true);

        $mediaRepositoryMock->shouldReceive('findWhere')
            ->times(3)
            ->andReturn(null);

        $mediaHelperMock->shouldReceive('isMediaUrlValid')
            ->times(9)
            ->andReturn(true);

        $mediaHelperMock->shouldReceive('isVideoUrl')
            ->times(3)
            ->andReturn(false);

        $mediaHelperMock->shouldReceive('getImageSize')
            ->times(3)
            ->andReturn(['width' => 800, 'height' => 600]);

        $mediaRepositoryMock->shouldReceive('create')
            ->times(3)
            ->andReturn($mockMedia1, $mockMedia2, $mockMedia3);

        $thumbnailRepositoryMock->shouldReceive('create')
            ->times(3)
            ->andReturn($mockThumbnail1, $mockThumbnail2, $mockThumbnail3);

        $retrievesImageProxyLinksMock->shouldReceive('execute')
            ->times(6)
            ->andReturn(['regular' => 'proxy_url', 'square' => 'proxy_url', 'wide' => 'proxy_url']);

        $service = new CreatesArticleMedia(
            $mediaRepositoryMock,
            $publisherEndpointRepositoryMock,
            $thumbnailRepositoryMock,
            $retrievesImageProxyLinksMock,
            $mediaHelperMock
        );

        $mediaIds = $service->execute($mockCoverImage, $mockMediaArray, $publisherId, $channelId);

        $this->assertEquals([1, 2, 3], $mediaIds);
    }

    public function testItItCreatesArticleWithExistingMedia(): void {
        $publisherId    = 123;
        $channelId      = 456;
        $mockCoverImage = [
            'url'     => $this->generator->url(),
            'caption' => $this->generator->sentence(),
        ];
        $mockMediaArray = [
            [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
        ];

        $mockMedia1     = Mockery::mock(Media::class);
        $mockMedia1->shouldReceive('getAttribute')->with('id')->andReturn(1);
        $mockMedia1->shouldReceive('getAttribute')->with('url')->andReturn($mockCoverImage['url']);

        $mockMedia2     = Mockery::mock(Media::class);
        $mockMedia2->shouldReceive('getAttribute')->with('id')->andReturn(2);
        $mockMedia2->shouldReceive('getAttribute')->with('url')->andReturn($mockMediaArray[0]['url']);

        $mockMedia3     = Mockery::mock(Media::class);
        $mockMedia3->shouldReceive('getAttribute')->with('id')->andReturn(3);
        $mockMedia3->shouldReceive('getAttribute')->with('url')->andReturn($mockMediaArray[1]['url']);

        $mockThumbnail1 = Mockery::mock(Thumbnail::class);
        $mockThumbnail2 = Mockery::mock(Thumbnail::class);
        $mockThumbnail3 = Mockery::mock(Thumbnail::class);

        $mediaRepositoryMock             = Mockery::mock(MediaRepository::class);
        $publisherEndpointRepositoryMock = Mockery::mock(PublisherEndpointRepository::class);
        $thumbnailRepositoryMock         = Mockery::mock(ThumbnailRepository::class);
        $retrievesImageProxyLinksMock    = Mockery::mock(RetrievesImageProxyLinks::class);
        $mediaHelperMock                 = Mockery::mock(MediaHelper::class);

        $publisherEndpointRepositoryMock
            ->shouldReceive('getHasProxyImage')
            ->once()
            ->with($publisherId, $channelId)
            ->andReturn(false);

        $mediaRepositoryMock->shouldReceive('findWhere')
            ->times(3)
            ->andReturn($mockMedia1, $mockMedia2, $mockMedia3);

        $mediaHelperMock->shouldReceive('isMediaUrlValid')
            ->times(6)
            ->andReturn(true);

        $thumbnailRepositoryMock->shouldReceive('create')
            ->times(3)
            ->andReturn($mockThumbnail1, $mockThumbnail2, $mockThumbnail3);

        $service = new CreatesArticleMedia(
            $mediaRepositoryMock,
            $publisherEndpointRepositoryMock,
            $thumbnailRepositoryMock,
            $retrievesImageProxyLinksMock,
            $mediaHelperMock
        );

        $mediaIds = $service->execute($mockCoverImage, $mockMediaArray, $publisherId, $channelId);

        $this->assertEquals([1, 2, 3], $mediaIds);
    }

    public function testItItCreatesArticleWithInvalidMedia(): void {
        $publisherId    = 123;
        $channelId      = 456;
        $mockCoverImage = [
            'url'     => $this->generator->url(),
            'caption' => $this->generator->sentence(),
        ];
        $mockMediaArray = [
            [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
        ];

        $mediaRepositoryMock             = Mockery::mock(MediaRepository::class);
        $publisherEndpointRepositoryMock = Mockery::mock(PublisherEndpointRepository::class);
        $thumbnailRepositoryMock         = Mockery::mock(ThumbnailRepository::class);
        $retrievesImageProxyLinksMock    = Mockery::mock(RetrievesImageProxyLinks::class);
        $mediaHelperMock                 = Mockery::mock(MediaHelper::class);

        $publisherEndpointRepositoryMock
            ->shouldReceive('getHasProxyImage')
            ->once()
            ->with($publisherId, $channelId)
            ->andReturn(false);

        $mediaRepositoryMock->shouldReceive('findWhere')
            ->times(3)
            ->andReturn(null);

        $mediaHelperMock->shouldReceive('isMediaUrlValid')
            ->times(3)
            ->andReturn(false);

        $service = new CreatesArticleMedia(
            $mediaRepositoryMock,
            $publisherEndpointRepositoryMock,
            $thumbnailRepositoryMock,
            $retrievesImageProxyLinksMock,
            $mediaHelperMock
        );

        $mediaIds = $service->execute($mockCoverImage, $mockMediaArray, $publisherId, $channelId);

        $this->assertEquals([], $mediaIds);
    }

    public function testItItCreatesArticleWithInvalidThumbnailUrl(): void {
        $publisherId    = 123;
        $channelId      = 456;
        $mockCoverImage = [
            'url'     => $this->generator->url(),
            'caption' => $this->generator->sentence(),
        ];
        $mockMediaArray = [
            [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
        ];

        $mockMedia1     = Mockery::mock(Media::class);
        $mockMedia1->shouldReceive('getAttribute')->with('id')->andReturn(1);
        $mockMedia1->shouldReceive('getAttribute')->with('url')->andReturn($mockCoverImage['url']);

        $mockMedia2     = Mockery::mock(Media::class);
        $mockMedia2->shouldReceive('getAttribute')->with('id')->andReturn(2);
        $mockMedia2->shouldReceive('getAttribute')->with('url')->andReturn($mockMediaArray[0]['url']);

        $mockMedia3     = Mockery::mock(Media::class);
        $mockMedia3->shouldReceive('getAttribute')->with('id')->andReturn(3);
        $mockMedia3->shouldReceive('getAttribute')->with('url')->andReturn($mockMediaArray[1]['url']);

        $mediaRepositoryMock             = Mockery::mock(MediaRepository::class);
        $publisherEndpointRepositoryMock = Mockery::mock(PublisherEndpointRepository::class);
        $thumbnailRepositoryMock         = Mockery::mock(ThumbnailRepository::class);
        $retrievesImageProxyLinksMock    = Mockery::mock(RetrievesImageProxyLinks::class);
        $mediaHelperMock                 = Mockery::mock(MediaHelper::class);

        $publisherEndpointRepositoryMock
            ->shouldReceive('getHasProxyImage')
            ->once()
            ->with($publisherId, $channelId)
            ->andReturn(false);

        $mediaRepositoryMock->shouldReceive('findWhere')
            ->times(3)
            ->andReturn(null);

        $mediaHelperMock->shouldReceive('isMediaUrlValid')
            ->with($mockCoverImage['url'])
            ->andReturn(true, false, false);

        $mediaHelperMock->shouldReceive('isMediaUrlValid')
            ->with($mockMediaArray[0]['url'])
            ->andReturn(true, false, false);

        $mediaHelperMock->shouldReceive('isMediaUrlValid')
            ->with($mockMediaArray[1]['url'])
            ->andReturn(true, false, false);

        $mediaHelperMock->shouldReceive('isVideoUrl')
            ->times(3)
            ->andReturn(false);

        $mediaHelperMock->shouldReceive('getImageSize')
            ->times(3)
            ->andReturn(['width' => 800, 'height' => 600]);

        $mediaRepositoryMock->shouldReceive('create')
            ->times(3)
            ->andReturn($mockMedia1, $mockMedia2, $mockMedia3);

        $thumbnailRepositoryMock->shouldNotReceive('create');

        $service = new CreatesArticleMedia(
            $mediaRepositoryMock,
            $publisherEndpointRepositoryMock,
            $thumbnailRepositoryMock,
            $retrievesImageProxyLinksMock,
            $mediaHelperMock
        );

        $mediaIds = $service->execute($mockCoverImage, $mockMediaArray, $publisherId, $channelId);

        $this->assertEquals([1, 2, 3], $mediaIds);
    }

    public function testItPreventsDuplicateMediaWhenCoverImageUrlExistsInMediaArray(): void {
        $publisherId    = 123;
        $channelId      = 456;
        $duplicateUrl   = $this->generator->url();

        $mockCoverImage = [
            'url'     => $duplicateUrl,
            'caption' => $this->generator->sentence(),
        ];
        $mockMediaArray = [
            [
                'url'     => $duplicateUrl, // Same URL as cover image
                'caption' => $this->generator->sentence(),
            ],
            [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
        ];

        $mockMedia1     = Mockery::mock(Media::class);
        $mockMedia1->shouldReceive('getAttribute')->with('id')->andReturn(1);
        $mockMedia1->shouldReceive('getAttribute')->with('url')->andReturn($duplicateUrl);

        $mockMedia2     = Mockery::mock(Media::class);
        $mockMedia2->shouldReceive('getAttribute')->with('id')->andReturn(2);
        $mockMedia2->shouldReceive('getAttribute')->with('url')->andReturn($mockMediaArray[1]['url']);

        $mediaRepositoryMock             = Mockery::mock(MediaRepository::class);
        $publisherEndpointRepositoryMock = Mockery::mock(PublisherEndpointRepository::class);
        $thumbnailRepositoryMock         = Mockery::mock(ThumbnailRepository::class);
        $retrievesImageProxyLinksMock    = Mockery::mock(RetrievesImageProxyLinks::class);
        $mediaHelperMock                 = Mockery::mock(MediaHelper::class);

        $publisherEndpointRepositoryMock
            ->shouldReceive('getHasProxyImage')
            ->once()
            ->with($publisherId, $channelId)
            ->andReturn(false);

        // Should only be called twice (once for cover image, once for unique media)
        $mediaRepositoryMock->shouldReceive('findWhere')
            ->twice()
            ->andReturn(null);

        $mediaHelperMock->shouldReceive('isMediaUrlValid')
            ->times(6) // 2 for cover image, 2 for first media (duplicate), 2 for second media
            ->andReturn(true);

        $mediaHelperMock->shouldReceive('isVideoUrl')
            ->twice() // Only called for the two unique media items
            ->andReturn(false);

        $mediaHelperMock->shouldReceive('getImageSize')
            ->twice() // Only called for the two unique media items
            ->andReturn(['width' => 800, 'height' => 600]);

        $mediaRepositoryMock->shouldReceive('create')
            ->twice() // Only called for the two unique media items
            ->andReturn($mockMedia1, $mockMedia2);

        $mockThumbnail1 = Mockery::mock(Thumbnail::class);
        $mockThumbnail2 = Mockery::mock(Thumbnail::class);

        $thumbnailRepositoryMock->shouldReceive('create')
            ->twice() // Only called for the two unique media items
            ->andReturn($mockThumbnail1, $mockThumbnail2);

        $service = new CreatesArticleMedia(
            $mediaRepositoryMock,
            $publisherEndpointRepositoryMock,
            $thumbnailRepositoryMock,
            $retrievesImageProxyLinksMock,
            $mediaHelperMock
        );

        $mediaIds = $service->execute($mockCoverImage, $mockMediaArray, $publisherId, $channelId);

        // Should only return 2 IDs (cover image and unique media, not the duplicate)
        $this->assertEquals([1, 2], $mediaIds);
    }
}
