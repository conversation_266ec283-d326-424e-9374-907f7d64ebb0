<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\ParserAdder\Services;

use App\Exceptions\ServiceException;
use App\Helpers\ContentHelper;
use App\Modules\ParserAdder\Services\SanitizesParsedContent;
use App\Repositories\PublisherRepository;
use Exception;
use Mockery;
use Tests\TestCase;

class SanitizesParsedContentTest extends TestCase {
    public function testItSanitizesParsedContent(): void {
        $parsedContent = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];
        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $publisherRepositoryMock->shouldReceive('find')
            ->with($mockPublisher->id)
            ->once()
            ->andReturn($mockPublisher);
        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')
            ->with($parsedContent['link'])
            ->once()
            ->andReturn($parsedContent['link']);

        $service          = new SanitizesParsedContent($publisherRepositoryMock, $contentHelperMock);
        $sanitizedContent = $service->execute($parsedContent, $mockPublisher->id);

        $this->assertArrayHasKey('description', $sanitizedContent);
        $this->assertArrayHasKey('full_content', $sanitizedContent);
        $this->assertArrayHasKey('link', $sanitizedContent);
    }

    public function testItSanitizesParsedContentForUA(): void {
        $parsedContent = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];
        $mockPublisher           = $this->createPublisher(['ga_id' => 'UA-123456789-1']);
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $publisherRepositoryMock->shouldReceive('find')
            ->with($mockPublisher->id)
            ->once()
            ->andReturn($mockPublisher);
        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')
            ->with($parsedContent['link'])
            ->once()
            ->andReturn($parsedContent['link']);

        $service          = new SanitizesParsedContent($publisherRepositoryMock, $contentHelperMock);
        $sanitizedContent = $service->execute($parsedContent, $mockPublisher->id);

        $this->assertArrayHasKey('description', $sanitizedContent);
        $this->assertArrayHasKey('full_content', $sanitizedContent);
        $this->assertArrayHasKey('link', $sanitizedContent);
    }

    public function testItSanitizesParsedContentWithLongDescription(): void {
        $parsedContent = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(300),
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];
        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $publisherRepositoryMock->shouldReceive('find')
            ->with($mockPublisher->id)
            ->once()
            ->andReturn($mockPublisher);
        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')
            ->with($parsedContent['link'])
            ->once()
            ->andReturn($parsedContent['link']);

        $service          = new SanitizesParsedContent($publisherRepositoryMock, $contentHelperMock);
        $sanitizedContent = $service->execute($parsedContent, $mockPublisher->id);

        $this->assertArrayHasKey('description', $sanitizedContent);
        $this->assertArrayHasKey('full_content', $sanitizedContent);
        $this->assertArrayHasKey('link', $sanitizedContent);
    }

    public function testItSanitizesParsedContentWithNoFirstImage(): void {
        $parsedContent = [
            'title'           => $this->generator->title(),
            'description'     => '',
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => '',
                'caption' => null,
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];
        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $publisherRepositoryMock->shouldReceive('find')
            ->with($mockPublisher->id)
            ->once()
            ->andReturn($mockPublisher);
        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')
            ->with($parsedContent['link'])
            ->once()
            ->andReturn($parsedContent['link']);

        $service          = new SanitizesParsedContent($publisherRepositoryMock, $contentHelperMock);
        $sanitizedContent = $service->execute($parsedContent, $mockPublisher->id);

        $this->assertArrayHasKey('description', $sanitizedContent);
        $this->assertArrayHasKey('full_content', $sanitizedContent);
        $this->assertArrayHasKey('link', $sanitizedContent);
    }

    public function testItSanitizesParsedContentWithMoreThanTwoParagraphs(): void {
        $parsedContent = [
            'title'           => $this->generator->title(),
            'description'     => '',
            'full_content'    => '<p>Paragraph 1</p><p>Paragraph 2</p><p>Paragraph 3</p>',
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => '',
                'caption' => null,
            ],
            'media'          => [],
            'author'         => $this->generator->name(),
            'published_date' => $this->generator->date(),
            'modified_date'  => $this->generator->date(),
        ];
        $mockPublisher           = $this->createPublisher();
        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $publisherRepositoryMock->shouldReceive('find')
            ->with($mockPublisher->id)
            ->once()
            ->andReturn($mockPublisher);
        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('getCanonicalUrl')
            ->with($parsedContent['link'])
            ->once()
            ->andReturn($parsedContent['link']);

        $service          = new SanitizesParsedContent($publisherRepositoryMock, $contentHelperMock);
        $sanitizedContent = $service->execute($parsedContent, $mockPublisher->id);

        $this->assertArrayHasKey('description', $sanitizedContent);
        $this->assertArrayHasKey('full_content', $sanitizedContent);
        $this->assertArrayHasKey('link', $sanitizedContent);
        $this->assertStringContainsString('<p>Paragraph 1</p><!--AD--><p>Paragraph 2</p><!--AD--><p>Paragraph 3</p><!--AD-->', $sanitizedContent['full_content']);

    }

    public function testItThrowsRuntimeExceptionOnFailure(): void {
        $parsedContent = [
            'title'           => $this->generator->title(),
            'description'     => $this->generator->sentence(),
            'full_content'    => $this->generator->paragraph(),
            'link'            => $this->generator->url(),
            'cover_image_url' => [
                'url'     => $this->generator->url(),
                'caption' => $this->generator->sentence(),
            ],
            'media' => [],
        ];

        $mockPublisher = $this->createPublisher();

        $publisherRepositoryMock = Mockery::mock(PublisherRepository::class);
        $publisherRepositoryMock->shouldReceive('find')
            ->with($mockPublisher->id)
            ->andThrow(new Exception());

        $contentHelperMock = Mockery::mock(ContentHelper::class);

        $service = new SanitizesParsedContent($publisherRepositoryMock, $contentHelperMock);

        $this->expectException(ServiceException::class);
        $service->execute($parsedContent, $mockPublisher->id);
    }
}
