<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\ParserAdder\Services;

use App\Modules\ParserAdder\Services\GeneratesUniqueIdForArticle;
use App\Repositories\ArticleRepository;
use Carbon\Carbon;
use Mockery;
use Tests\TestCase;

class GeneratesUniqueIdForArticleTest extends TestCase {
    public function testItGeneratesUniqueIdForArticle(): void {
        $canonicalUrl = $this->generator->url();
        $channelId    = $this->generator->numberBetween(1, 1000);

        $articleRepositoryMock = Mockery::mock(ArticleRepository::class);
        $articleRepositoryMock->shouldReceive('findWhere')
            ->with(['canonicalURL' => $canonicalUrl, 'channelID' => $channelId])
            ->once()
            ->andReturn(null);

        $articleRepositoryMock->shouldReceive('findWhere')
            ->with(Mockery::on(function ($arg) {
                return isset($arg['uniqueID']) && is_string($arg['uniqueID']);
            }))
            ->once()
            ->andReturn(null);

        $service  = new GeneratesUniqueIdForArticle($articleRepositoryMock);
        $uniqueId = $service->execute($canonicalUrl, $channelId);

        $expectedPrefix = 'A' . Carbon::now()->format('ym') . '_';
        $this->assertStringStartsWith($expectedPrefix, $uniqueId);
        $this->assertEquals(strlen($expectedPrefix) + 6, strlen($uniqueId));
    }

    public function testItReturnsExistingUniqueIdWhenArticleExists(): void {
        $canonicalUrl     = $this->generator->url();
        $channelId        = $this->generator->numberBetween(1, 1000);
        $existingUniqueId = 'A2506_existing';

        $existingArticle = $this->createArticle(['uniqueID' => $existingUniqueId]);

        $articleRepositoryMock = Mockery::mock(ArticleRepository::class);
        $articleRepositoryMock->shouldReceive('findWhere')
            ->with(['canonicalURL' => $canonicalUrl, 'channelID' => $channelId])
            ->once()
            ->andReturn($existingArticle);

        $service  = new GeneratesUniqueIdForArticle($articleRepositoryMock);
        $uniqueId = $service->execute($canonicalUrl, $channelId);

        $this->assertEquals($existingUniqueId, $uniqueId);
    }
}
