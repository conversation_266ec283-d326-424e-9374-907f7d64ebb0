<?php

declare(strict_types=1);

namespace Tests\Unit\Modules\ParserAdder\Logics;

use App\Classes\ValueObjects\ArticleDataWithPredictionDataObject;
use App\Classes\ValueObjects\ArticleObject;
use App\Classes\ValueObjects\ParsedArticleObject;
use App\Exceptions\ArticleAdderException;
use App\Helpers\ContentHelper;
use App\Modules\ParserAdder\Logics\PopulateArticleDataIntoDatabaseLogic;
use App\Modules\ParserAdder\Services\CreatesArticle;
use App\Modules\ParserAdder\Services\CreatesArticleMedia;
use App\Modules\ParserAdder\Services\CreatesPrediction;
use App\Modules\ParserAdder\Services\GeneratesUniqueIdForArticle;
use App\Modules\ParserAdder\Services\PopulatesArticleWordCount;
use App\Modules\ParserAdder\Services\UpdatesArticle;
use App\Repositories\ArticleRepository;
use App\Repositories\PredictionsRepository;
use App\Services\NewswavOutboxClient;
use Mockery;
use Tests\TestCase;

class PopulateArticleDataIntoDatabaseLogicTest extends TestCase {
    public function testItCreatesArticle(): void {
        $publisherId    = 10;
        $channelId      = 20;
        $articleId      = 100;
        $title          = $this->generator->title();
        $description    = $this->generator->sentence();
        $fullContent    = $this->generator->paragraph();
        $author         = $this->generator->name();
        $publishedDate  = $this->generator->date();
        $modifiedDate   = $this->generator->date();
        $canonicalUrl   = $this->generator->url();
        $coverImage     = ['url' => $this->generator->url()];
        $media          = [$this->generator->url()];
        $contentMd5     = $this->generator->md5();
        $uniqueId       = 'A2506_existing';
        $slugifiedTitle = 'slugified-title';

        $mockArticle = $this->createArticle(['articleID' => $articleId, 'channelID' => $channelId]);

        $parsedArticle = Mockery::mock(ParsedArticleObject::class);
        $parsedArticle->shouldReceive('getArticleId')->andReturn($articleId);
        $parsedArticle->shouldReceive('getCoverImage')->andReturn($coverImage);
        $parsedArticle->shouldReceive('getMedia')->andReturn($media);
        $parsedArticle->shouldReceive('getFullContent')->andReturn($fullContent);
        $parsedArticle->shouldReceive('getPublishedDate')->andReturn($publishedDate);
        $parsedArticle->shouldReceive('getTitle')->andReturn($title);
        $parsedArticle->shouldReceive('getDescription')->andReturn($description);
        $parsedArticle->shouldReceive('getAuthor')->andReturn($author);
        $parsedArticle->shouldReceive('getModifiedDate')->andReturn($modifiedDate);
        $parsedArticle->shouldReceive('getCanonicalURL')->andReturn($canonicalUrl);
        $parsedArticle->shouldReceive('getContentMd5')->andReturn($contentMd5);

        $generatesUniqueIdForArticleMock = Mockery::mock(GeneratesUniqueIdForArticle::class);
        $generatesUniqueIdForArticleMock->shouldReceive('execute')->once()->with($canonicalUrl, $channelId)->andReturn($uniqueId);

        $createsArticleMediaMock = Mockery::mock(CreatesArticleMedia::class);
        $createsArticleMediaMock->shouldReceive('execute')->once()->andReturn(['media_id_1']);

        $populatesArticleWordCountMock = Mockery::mock(PopulatesArticleWordCount::class);
        $populatesArticleWordCountMock->shouldReceive('execute')->once()->with($uniqueId, $fullContent);

        $createsArticleMock = Mockery::mock(CreatesArticle::class);
        $createsArticleMock->shouldReceive('execute')->once()->with(Mockery::type(ArticleObject::class))
            ->andReturn($mockArticle);

        $updatesArticleMock = Mockery::mock(UpdatesArticle::class);
        $updatesArticleMock->shouldReceive('execute')->never(); // no update in this test

        $createsPredictionMock = Mockery::mock(CreatesPrediction::class);
        $createsPredictionMock->shouldReceive('execute')->never(); // no prediction logic in this test

        $articleRepositoryMock = Mockery::mock(ArticleRepository::class);
        $articleRepositoryMock->shouldReceive('findWhere')->once()->with(['canonicalURL' => $canonicalUrl, 'channelID' => $channelId])->andReturn(null);

        $predictionRepositoryMock = Mockery::mock(PredictionsRepository::class);

        $newswavOutboxClientMock = Mockery::mock(NewswavOutboxClient::class);

        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('slugifyTitle')->once()->with($title)->andReturn($slugifiedTitle);

        $logic = new PopulateArticleDataIntoDatabaseLogic(
            $generatesUniqueIdForArticleMock,
            $createsArticleMediaMock,
            $populatesArticleWordCountMock,
            $createsArticleMock,
            $updatesArticleMock,
            $createsPredictionMock,
            $articleRepositoryMock,
            $predictionRepositoryMock,
            $newswavOutboxClientMock,
            $contentHelperMock
        );

        $logic->execute($parsedArticle, $publisherId, $channelId);

        $this->assertTrue(true);
    }

    public function testItCreatesArticleToExcludePublishedDate(): void {
        $publisherId    = 10;
        $channelId      = 492;
        $articleId      = 100;
        $title          = $this->generator->title();
        $description    = $this->generator->sentence();
        $fullContent    = $this->generator->paragraph();
        $author         = $this->generator->name();
        $publishedDate  = $this->generator->date();
        $modifiedDate   = $this->generator->date();
        $canonicalUrl   = $this->generator->url();
        $coverImage     = ['url' => $this->generator->url()];
        $media          = [$this->generator->url()];
        $contentMd5     = $this->generator->md5();
        $uniqueId       = 'A2506_existing';
        $slugifiedTitle = 'slugified-title';

        $mockArticle = $this->createArticle(['articleID' => $articleId, 'channelID' => $channelId]);

        $parsedArticle = Mockery::mock(ParsedArticleObject::class);
        $parsedArticle->shouldReceive('getArticleId')->andReturn($articleId);
        $parsedArticle->shouldReceive('getCoverImage')->andReturn($coverImage);
        $parsedArticle->shouldReceive('getMedia')->andReturn($media);
        $parsedArticle->shouldReceive('getFullContent')->andReturn($fullContent);
        $parsedArticle->shouldReceive('getPublishedDate')->andReturn($publishedDate);
        $parsedArticle->shouldReceive('setPublishedDate')->once();
        $parsedArticle->shouldReceive('getTitle')->andReturn($title);
        $parsedArticle->shouldReceive('getDescription')->andReturn($description);
        $parsedArticle->shouldReceive('getAuthor')->andReturn($author);
        $parsedArticle->shouldReceive('getModifiedDate')->andReturn($modifiedDate);
        $parsedArticle->shouldReceive('getCanonicalURL')->andReturn($canonicalUrl);
        $parsedArticle->shouldReceive('getContentMd5')->andReturn($contentMd5);

        $generatesUniqueIdForArticleMock = Mockery::mock(GeneratesUniqueIdForArticle::class);
        $generatesUniqueIdForArticleMock->shouldReceive('execute')->once()->with($canonicalUrl, $channelId)->andReturn($uniqueId);

        $createsArticleMediaMock = Mockery::mock(CreatesArticleMedia::class);
        $createsArticleMediaMock->shouldReceive('execute')->once()->andReturn(['media_id_1']);

        $populatesArticleWordCountMock = Mockery::mock(PopulatesArticleWordCount::class);
        $populatesArticleWordCountMock->shouldReceive('execute')->once()->with($uniqueId, $fullContent);

        $createsArticleMock = Mockery::mock(CreatesArticle::class);
        $createsArticleMock->shouldReceive('execute')->once()->with(Mockery::type(ArticleObject::class))
            ->andReturn($mockArticle);

        $updatesArticleMock = Mockery::mock(UpdatesArticle::class);
        $updatesArticleMock->shouldReceive('execute')->never(); // no update in this test

        $createsPredictionMock = Mockery::mock(CreatesPrediction::class);
        $createsPredictionMock->shouldReceive('execute')->never(); // no prediction logic in this test

        $articleRepositoryMock = Mockery::mock(ArticleRepository::class);
        $articleRepositoryMock->shouldReceive('findWhere')->once()->with(['canonicalURL' => $canonicalUrl, 'channelID' => $channelId])->andReturn(null);

        $predictionRepositoryMock = Mockery::mock(PredictionsRepository::class);

        $newswavOutboxClientMock = Mockery::mock(NewswavOutboxClient::class);

        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('slugifyTitle')->once()->with($title)->andReturn($slugifiedTitle);
        $logic = new PopulateArticleDataIntoDatabaseLogic(
            $generatesUniqueIdForArticleMock,
            $createsArticleMediaMock,
            $populatesArticleWordCountMock,
            $createsArticleMock,
            $updatesArticleMock,
            $createsPredictionMock,
            $articleRepositoryMock,
            $predictionRepositoryMock,
            $newswavOutboxClientMock,
            $contentHelperMock
        );

        $logic->execute($parsedArticle, $publisherId, $channelId);

        $this->assertTrue(true);
    }

    public function testItCreatesArticleToBypassPrediction(): void {
        $publisherId    = 20;
        $channelId      = 20;
        $articleId      = 100;
        $title          = $this->generator->title();
        $description    = $this->generator->sentence();
        $fullContent    = $this->generator->paragraph();
        $author         = $this->generator->name();
        $publishedDate  = $this->generator->date();
        $modifiedDate   = $this->generator->date();
        $canonicalUrl   = $this->generator->url();
        $coverImage     = ['url' => $this->generator->url()];
        $media          = [$this->generator->url()];
        $contentMd5     = $this->generator->md5();
        $uniqueId       = 'A2506_existing';
        $slugifiedTitle = 'slugified-title';

        $mockArticle    = $this->createArticle();
        $mockPrediction = $this->createPrediction(['unique_id' => $uniqueId]);

        $parsedArticle = Mockery::mock(ParsedArticleObject::class);
        $parsedArticle->shouldReceive('getArticleId')->andReturn($articleId);
        $parsedArticle->shouldReceive('getCoverImage')->andReturn($coverImage);
        $parsedArticle->shouldReceive('getMedia')->andReturn($media);
        $parsedArticle->shouldReceive('getFullContent')->andReturn($fullContent);
        $parsedArticle->shouldReceive('getPublishedDate')->andReturn($publishedDate);
        $parsedArticle->shouldReceive('getTitle')->andReturn($title);
        $parsedArticle->shouldReceive('getDescription')->andReturn($description);
        $parsedArticle->shouldReceive('getAuthor')->andReturn($author);
        $parsedArticle->shouldReceive('getModifiedDate')->andReturn($modifiedDate);
        $parsedArticle->shouldReceive('getCanonicalURL')->andReturn($canonicalUrl);
        $parsedArticle->shouldReceive('getContentMd5')->andReturn($contentMd5);

        $generatesUniqueIdForArticleMock = Mockery::mock(GeneratesUniqueIdForArticle::class);
        $generatesUniqueIdForArticleMock->shouldReceive('execute')->once()->with($canonicalUrl, $channelId)->andReturn($uniqueId);

        $createsArticleMediaMock = Mockery::mock(CreatesArticleMedia::class);
        $createsArticleMediaMock->shouldReceive('execute')->once()->andReturn(['media_id_1']);

        $populatesArticleWordCountMock = Mockery::mock(PopulatesArticleWordCount::class);
        $populatesArticleWordCountMock->shouldReceive('execute')->once()->with($uniqueId, $fullContent);

        $createsArticleMock = Mockery::mock(CreatesArticle::class);
        $createsArticleMock->shouldReceive('execute')->once()->with(Mockery::type(ArticleObject::class))
            ->andReturn($mockArticle);

        $updatesArticleMock = Mockery::mock(UpdatesArticle::class);
        $updatesArticleMock->shouldReceive('execute')->never(); // no update in this test

        $articleRepositoryMock = Mockery::mock(ArticleRepository::class);
        $articleRepositoryMock->shouldReceive('findWhere')->once()->with(['canonicalURL' => $canonicalUrl, 'channelID' => $channelId])->andReturn(null);

        $predictionRepositoryMock = Mockery::mock(PredictionsRepository::class);
        $predictionRepositoryMock->shouldReceive('getArticleDataWithPredictionData')->once()->with($uniqueId)->andReturn($mockPrediction);

        $createsPredictionMock = Mockery::mock(CreatesPrediction::class);
        $createsPredictionMock->shouldReceive('execute')->once()->with($mockArticle);

        $newswavOutboxClientMock = Mockery::mock(NewswavOutboxClient::class);
        $newswavOutboxClientMock->shouldReceive('emitMessage')->once()->with('feed', Mockery::type(ArticleDataWithPredictionDataObject::class), 'contentId', 'internal.static.prediction');

        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('slugifyTitle')->once()->with($title)->andReturn($slugifiedTitle);

        $logic = new PopulateArticleDataIntoDatabaseLogic(
            $generatesUniqueIdForArticleMock,
            $createsArticleMediaMock,
            $populatesArticleWordCountMock,
            $createsArticleMock,
            $updatesArticleMock,
            $createsPredictionMock,
            $articleRepositoryMock,
            $predictionRepositoryMock,
            $newswavOutboxClientMock,
            $contentHelperMock
        );

        $logic->execute($parsedArticle, $publisherId, $channelId);

        $this->assertTrue(true);
    }

    public function testItUpdatesArticle(): void {
        $publisherId    = 10;
        $channelId      = 20;
        $articleId      = 100;
        $title          = $this->generator->title();
        $description    = $this->generator->sentence();
        $fullContent    = $this->generator->paragraph();
        $author         = $this->generator->name();
        $publishedDate  = $this->generator->date();
        $modifiedDate   = $this->generator->date();
        $canonicalUrl   = $this->generator->url();
        $coverImage     = ['url' => $this->generator->url()];
        $media          = [$this->generator->url()];
        $contentMd5     = $this->generator->md5();
        $uniqueId       = 'A2506_existing';
        $slugifiedTitle = 'slugified-title';

        $mockArticle = $this->createArticle(['articleID' => $articleId, 'channelID' => $channelId]);

        $parsedArticle = Mockery::mock(ParsedArticleObject::class);
        $parsedArticle->shouldReceive('getArticleId')->andReturn($articleId);
        $parsedArticle->shouldReceive('getCoverImage')->andReturn($coverImage);
        $parsedArticle->shouldReceive('getMedia')->andReturn($media);
        $parsedArticle->shouldReceive('getFullContent')->andReturn($fullContent);
        $parsedArticle->shouldReceive('getPublishedDate')->andReturn($publishedDate);
        $parsedArticle->shouldReceive('getTitle')->andReturn($title);
        $parsedArticle->shouldReceive('getDescription')->andReturn($description);
        $parsedArticle->shouldReceive('getAuthor')->andReturn($author);
        $parsedArticle->shouldReceive('getModifiedDate')->andReturn($modifiedDate);
        $parsedArticle->shouldReceive('getCanonicalURL')->andReturn($canonicalUrl);
        $parsedArticle->shouldReceive('getContentMd5')->andReturn($contentMd5);

        $generatesUniqueIdForArticleMock = Mockery::mock(GeneratesUniqueIdForArticle::class);
        $generatesUniqueIdForArticleMock->shouldReceive('execute')->once()->with($canonicalUrl, $channelId)->andReturn($uniqueId);

        $createsArticleMediaMock = Mockery::mock(CreatesArticleMedia::class);
        $createsArticleMediaMock->shouldReceive('execute')->once()->andReturn(['media_id_1']);

        $populatesArticleWordCountMock = Mockery::mock(PopulatesArticleWordCount::class);
        $populatesArticleWordCountMock->shouldReceive('execute')->once()->with($uniqueId, $fullContent);

        $createsArticleMock = Mockery::mock(CreatesArticle::class);
        $createsArticleMock->shouldReceive('execute')->never(); // no create in this test

        $updatesArticleMock = Mockery::mock(UpdatesArticle::class);
        $updatesArticleMock->shouldReceive('execute')->once()->with(Mockery::type(ArticleObject::class));

        $createsPredictionMock = Mockery::mock(CreatesPrediction::class);
        $createsPredictionMock->shouldReceive('execute')->never(); // no prediction logic in this test

        $articleRepositoryMock = Mockery::mock(ArticleRepository::class);
        $articleRepositoryMock->shouldReceive('findWhere')->once()->with(['canonicalURL' => $canonicalUrl, 'channelID' => $channelId])->andReturn($mockArticle);

        $predictionRepositoryMock = Mockery::mock(PredictionsRepository::class);

        $newswavOutboxClientMock = Mockery::mock(NewswavOutboxClient::class);

        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('slugifyTitle')->once()->with($title)->andReturn($slugifiedTitle);

        $logic = new PopulateArticleDataIntoDatabaseLogic(
            $generatesUniqueIdForArticleMock,
            $createsArticleMediaMock,
            $populatesArticleWordCountMock,
            $createsArticleMock,
            $updatesArticleMock,
            $createsPredictionMock,
            $articleRepositoryMock,
            $predictionRepositoryMock,
            $newswavOutboxClientMock,
            $contentHelperMock
        );

        $logic->execute($parsedArticle, $publisherId, $channelId);

        $this->assertTrue(true);
    }

    public function testItThrowsExceptionOnFailure(): void {
        $publisherId    = 10;
        $channelId      = 20;
        $articleId      = 100;
        $title          = $this->generator->title();
        $description    = $this->generator->sentence();
        $fullContent    = $this->generator->paragraph();
        $author         = $this->generator->name();
        $publishedDate  = $this->generator->date();
        $modifiedDate   = $this->generator->date();
        $canonicalUrl   = $this->generator->url();
        $coverImage     = ['url' => $this->generator->url()];
        $media          = [$this->generator->url()];
        $contentMd5     = $this->generator->md5();
        $uniqueId       = 'A2506_existing';
        $slugifiedTitle = 'slugified-title';

        $parsedArticle = Mockery::mock(ParsedArticleObject::class);
        $parsedArticle->shouldReceive('getArticleId')->andReturn($articleId);
        $parsedArticle->shouldReceive('getCoverImage')->andReturn($coverImage);
        $parsedArticle->shouldReceive('getMedia')->andReturn($media);
        $parsedArticle->shouldReceive('getFullContent')->andReturn($fullContent);
        $parsedArticle->shouldReceive('getPublishedDate')->andReturn($publishedDate);
        $parsedArticle->shouldReceive('getTitle')->andReturn($title);
        $parsedArticle->shouldReceive('getDescription')->andReturn($description);
        $parsedArticle->shouldReceive('getAuthor')->andReturn($author);
        $parsedArticle->shouldReceive('getModifiedDate')->andReturn($modifiedDate);
        $parsedArticle->shouldReceive('getCanonicalURL')->andReturn($canonicalUrl);
        $parsedArticle->shouldReceive('getContentMd5')->andReturn($contentMd5);

        $generatesUniqueIdForArticleMock = Mockery::mock(GeneratesUniqueIdForArticle::class);
        $generatesUniqueIdForArticleMock->shouldReceive('execute')->once()->with($canonicalUrl, $channelId)->andReturn($uniqueId);

        $createsArticleMediaMock = Mockery::mock(CreatesArticleMedia::class);
        $createsArticleMediaMock->shouldReceive('execute')->once()->andReturn(['media_id_1']);

        $populatesArticleWordCountMock = Mockery::mock(PopulatesArticleWordCount::class);
        $populatesArticleWordCountMock->shouldReceive('execute')->once()->with($uniqueId, $fullContent);

        $createsArticleMock = Mockery::mock(CreatesArticle::class);
        $createsArticleMock->shouldReceive('execute')->once()->with(Mockery::type(ArticleObject::class))
            ->andThrow(new ArticleAdderException(500, 'Failed to create article'));

        $updatesArticleMock = Mockery::mock(UpdatesArticle::class);
        $updatesArticleMock->shouldReceive('execute')->never(); // no update in this test

        $createsPredictionMock = Mockery::mock(CreatesPrediction::class);
        $createsPredictionMock->shouldReceive('execute')->never(); // no prediction logic in this test

        $articleRepositoryMock = Mockery::mock(ArticleRepository::class);
        $articleRepositoryMock->shouldReceive('findWhere')->once()->with(['canonicalURL' => $canonicalUrl, 'channelID' => $channelId])->andReturn(null);

        $predictionRepositoryMock = Mockery::mock(PredictionsRepository::class);

        $newswavOutboxClientMock = Mockery::mock(NewswavOutboxClient::class);

        $contentHelperMock = Mockery::mock(ContentHelper::class);
        $contentHelperMock->shouldReceive('slugifyTitle')->once()->with($title)->andReturn($slugifiedTitle);

        $logic = new PopulateArticleDataIntoDatabaseLogic(
            $generatesUniqueIdForArticleMock,
            $createsArticleMediaMock,
            $populatesArticleWordCountMock,
            $createsArticleMock,
            $updatesArticleMock,
            $createsPredictionMock,
            $articleRepositoryMock,
            $predictionRepositoryMock,
            $newswavOutboxClientMock,
            $contentHelperMock
        );

        $this->expectException(ArticleAdderException::class);
        $logic->execute($parsedArticle, $publisherId, $channelId);
    }
}
