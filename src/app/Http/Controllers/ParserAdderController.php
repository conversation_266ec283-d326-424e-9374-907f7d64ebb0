<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Classes\Constants\Parser;
use App\Classes\ValueObjects\ParsedArticleObject;
use App\Http\Requests\ParseArticleDataRequest;
use App\Modules\ParserAdder\Logics\ParseArticleDataManuallyLogic;
use App\Modules\ParserAdder\Logics\PopulateArticleDataIntoDatabaseLogic;
use Illuminate\Http\JsonResponse;

class ParserAdderController extends Controller {
    public function parseArticleDataManually(ParseArticleDataRequest $request, ParseArticleDataManuallyLogic $parseArticleDataManuallyLogic, PopulateArticleDataIntoDatabaseLogic $populateArticleDataIntoDatabaseLogic): JsonResponse {
        $articleParsed            = $parseArticleDataManuallyLogic->execute($request);
        $populatedArticleUniqueId = $populateArticleDataIntoDatabaseLogic->execute(new ParsedArticleObject(
            $articleParsed[Parser::ARTICLE_ID],
            $articleParsed[Parser::TITLE],
            $articleParsed[Parser::DESCRIPTION],
            $articleParsed[Parser::FULL_CONTENT],
            $articleParsed[Parser::AUTHOR],
            $articleParsed[Parser::PUBLISHED_DATE],
            $articleParsed[Parser::MODIFIED_DATE],
            $articleParsed[Parser::LINK],
            $articleParsed[Parser::LINK],
            $articleParsed[Parser::COVER_IMAGE_URL],
            $articleParsed[Parser::MEDIA],
            $articleParsed[Parser::CONTENT_MD5],
        ), $request->input('publisher_id'), $request->input('channel_id'));

        return response()->json(['unique_id' => $populatedArticleUniqueId]);
    }
}
