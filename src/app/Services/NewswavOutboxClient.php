<?php

declare(strict_types=1);

namespace App\Services;

use App\Classes\Constants\Parser;
use App\Classes\ValueObjects\ArticleDataWithPredictionDataObject;
use App\Exceptions\APIException;
use Carbon\Carbon;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Psr\Http\Message\ResponseInterface;

class NewswavOutboxClient {
    public function __construct(private Client $client) {
    }

    public function emitMessage(
        string $topic,
        ArticleDataWithPredictionDataObject $articleDataWithPredictionDataObject,
        string $keyField,
        string $eventName,
    ): void {
        $payload = $this->getPayload($articleDataWithPredictionDataObject);
        $this->makeRequest('', 'POST', [
            'topic'        => $topic,
            'payload'      => [
                'eventName' => $eventName,
                'payload'   => $payload,
            ],
            'service_name' => gethostname(),
            'key'          => $this->extractKey($keyField, $payload),
        ]);
    }

    private function getPayload(ArticleDataWithPredictionDataObject $articleDataWithPredictionDataObject): array {
        $prediction = $articleDataWithPredictionDataObject->getPrediction();
        $article    = $articleDataWithPredictionDataObject->getArticle();
        $channel    = $articleDataWithPredictionDataObject->getChannel();
        $publisher  = $articleDataWithPredictionDataObject->getPublisher();

        return [
            'contentId'        => $article->uniqueID,
            'title'            => $article->title,
            'thumbnailUrl'     => $articleDataWithPredictionDataObject->getThumbnailURL(),
            'imageAspectRatio' => $this->calculateAspectRatio($articleDataWithPredictionDataObject->getWideImageWidth(), $articleDataWithPredictionDataObject->getWideImageHeight()),
            'description'      => $article->description,
            'publisher'        => (object) [
                'id'                      => $publisher->id,
                'name'                    => $publisher->name,
                'url'                     => $publisher->website_url,
                'project'                 => $publisher->project,
                'enabled'                 => $publisher->enabled === 1,
                'imageUrl'                => $publisher->logo_url,
                'isVerified'              => (bool) $publisher->verified,
                'permalink'               => $publisher->permalink,
                'isNewswav'               => in_array((int) $publisher->id, Parser::NEWSWAV_PUBLISHER_IDS, true),
                'showFollowButton'        => in_array((int) $publisher->id, Parser::HIDE_FOLLOW_BUTTON_PUBLISHER_IDS, true) === false,
                'showHideButton'          => in_array((int) $publisher->id, Parser::HIDE_HIDE_BUTTON_PUBLISHER_IDS, true) === false,
                'showNotInterestedButton' => in_array((int) $publisher->id, Parser::HIDE_HIDE_BUTTON_PUBLISHER_IDS, true) === false,
            ],
            'publishedAt' => Carbon::parse($article->publishedDate)->toIso8601ZuluString(),
            'language'    => strtolower($channel->language),
            'topics'      => $prediction->topic !== -1
                ? [[
                    'id' => $prediction->topic,
                    'en' => $prediction->topic_en,
                    'ms' => $prediction->topic_ms,
                    'zh' => $prediction->topic_zh,
                ]]
                : [],
            'native'              => $channel->reader_view_only === 1,
            'type'                => 'article',
            'showOriginalArticle' => in_array($publisher->project, Parser::PROJECTS_TO_SHOW, true) === false,
            'permalink'           => $article->permalink,
            'deleted'             => false,
            'originalUrl'         => $article->canonicalURL,
            'host'                => ['hostname' => gethostname()],
        ];
    }

    /**
     * @param array<string,mixed> $parameters
     *
     * @throws APIException
     */
    private function makeRequest(string $endpoint, string $method, array $parameters): ResponseInterface {
        try {
            $response = $this->client->request($method, config('newswav_services.outbox_service.base_url') . $endpoint, [
                'headers' => [
                    'Content-Type'                                       => 'application/json',
                    config('newswav_services.outbox_service.header_key') => config('newswav_services.outbox_service.api_key'),
                ],
                'json' => $parameters,
            ]);

            return $response;
        } catch (GuzzleException $exception) {
            throw new APIException($exception->getCode(), $exception->getMessage());
        }
    }

    private function extractKey(string $keyPath, array $payload): ?string {
        $keys  = explode('.', $keyPath);
        $value = $payload;
        foreach ($keys as $key) {
            if (is_array($value) && isset($value[$key])) {
                $value = $value[$key];
            } else {
                return null;
            }
        }

        return (string) $value;
    }

    private function calculateAspectRatio(int $width, int $height): string {
        $gcd = function ($a, $b) use (&$gcd) {
            return ($b === 0) ? $a : $gcd($b, $a % $b);
        };

        $divisor = $gcd($width, $height);

        $aspectRatioWidth  = $width / $divisor;
        $aspectRatioHeight = $height / $divisor;

        return $aspectRatioWidth . ':' . $aspectRatioHeight;
    }
}
