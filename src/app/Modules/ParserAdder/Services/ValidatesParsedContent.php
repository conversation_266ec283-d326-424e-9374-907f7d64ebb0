<?php

declare(strict_types=1);

namespace App\Modules\ParserAdder\Services;

use App\Classes\Constants\Parser;
use App\Classes\Constants\ServerParameters;
use App\Exceptions\ServiceException;
use App\Helpers\MediaHelper;
use App\Repositories\PublisherRepository;
use Carbon\Carbon;
use Throwable;

class ValidatesParsedContent {
    public function __construct(
        private PublisherRepository $publisherRepository,
        private MediaHelper $mediaHelper,
    ) {
    }

    /**
     * @throws ServiceException
     */
    public function execute(array $parsedContent, int $publisherId): array {
        $validated = [];
        foreach (Parser::REQUIRED_FIELDS as $field) {
            if (empty($parsedContent[$field])) {
                throw new ServiceException(ServerParameters::HTTP_STATUS_INTERNAL_SERVER_ERROR, "Missing required field: {$field}");
            }
        }

        $validated[Parser::MEDIA] = $this->validateMedia($parsedContent[Parser::FULL_CONTENT], $parsedContent[Parser::MEDIA]);

        foreach (Parser::DEFAULT_PARSED_CONTENT_FIELDS as $key => $defaultField) {
            $validated[$key] = match ($key) {
                Parser::ARTICLE_ID      => $this->validateArticleId($parsedContent),
                Parser::TITLE           => $this->validateTitle($parsedContent),
                Parser::DESCRIPTION     => $this->validateDescription($parsedContent),
                Parser::FULL_CONTENT    => $this->validateFullContent($parsedContent),
                Parser::AUTHOR          => $this->validateAuthor($parsedContent, $publisherId),
                Parser::PUBLISHED_DATE  => $this->validatePublishedDate($parsedContent),
                Parser::MODIFIED_DATE   => $this->validateModifiedDate($parsedContent),
                Parser::COVER_IMAGE_URL => $this->validateCoverImage($parsedContent, $defaultField, $validated[Parser::MEDIA]),
                default                 => $parsedContent[$key] ?? $defaultField,
            };
        }

        return $validated;
    }

    private function validateArticleId(array $content): int {
        if (empty($content[Parser::ARTICLE_ID]) === false && is_int($content[Parser::ARTICLE_ID])) {
            $articleId = $content[Parser::ARTICLE_ID];

            if ($articleId > 0 && $articleId <= Parser::MAX_ARTICLE_ID) {
                return $articleId;
            }
        }

        return 0;
    }

    private function validateTitle(array $content): string {
        $title = html_entity_decode($content[Parser::TITLE] ?? '', ENT_QUOTES | ENT_HTML5, 'UTF-8');
        return mb_substr($title, 0, Parser::TITLE_MAX_LENGTH);
    }

    private function validateDescription(array $content): string {
        if (empty($content[Parser::DESCRIPTION]) === false) {
            $description = html_entity_decode($content[Parser::DESCRIPTION], ENT_QUOTES | ENT_HTML5, 'UTF-8');
            return mb_substr(strip_tags($description), 0, Parser::DESCRIPTION_LENGTH);
        }

        $fullContent = html_entity_decode($content[Parser::FULL_CONTENT] ?? '', ENT_QUOTES | ENT_HTML5, 'UTF-8');
        return mb_substr(strip_tags($fullContent), 0, Parser::DESCRIPTION_LENGTH) . '...';
    }

    private function validateFullContent(array $content): string {
        return html_entity_decode($content[Parser::FULL_CONTENT] ?? '', ENT_QUOTES | ENT_HTML5, 'UTF-8');
    }

    private function validateAuthor(array $content, int $publisherId): string {
        if (empty($content[Parser::AUTHOR]) === false) {
            return mb_substr($content[Parser::AUTHOR], 0, Parser::AUTHOR_MAX_LENGTH);
        }

        $publisher = $this->publisherRepository->findWhere(['id' => $publisherId]);

        return mb_substr($publisher->name, 0, Parser::AUTHOR_MAX_LENGTH);
    }

    private function validatePublishedDate(array $content): string {
        if (empty($content[Parser::PUBLISHED_DATE])) {
            return Carbon::now()->toDateTimeString();
        }

        try {
            return Carbon::parse($content[Parser::PUBLISHED_DATE])->toDateTimeString();
        } catch (Throwable $e) {
            return Carbon::now()->toDateTimeString();
        }
    }

    private function validateModifiedDate(array $content): string {
        if (empty($content[Parser::MODIFIED_DATE])) {
            return Carbon::now()->toDateTimeString();
        }

        try {
            return Carbon::parse($content[Parser::MODIFIED_DATE])->toDateTimeString();
        } catch (Throwable $e) {
            return Carbon::now()->toDateTimeString();
        }
    }

    private function validateCoverImage(array $content, array $default, array $validatedMedia): array {
        if (empty($content[Parser::COVER_IMAGE_URL]['url']) === false) {

            $coverImage = [
                'url'     => $content[Parser::COVER_IMAGE_URL]['url'],
                'caption' => $content[Parser::COVER_IMAGE_URL]['caption'] ?? null,
            ];

            return $coverImage;
        }

        if (empty($validatedMedia) === false) {
            foreach ($validatedMedia as $media) {
                if (empty($media['url']) === false && $this->mediaHelper->isImageUrl($media['url']) === true) {
                    return [
                        'url'     => $media['url'],
                        'caption' => $media['caption'] ?? null,
                    ];
                }
            }
        }

        return $default;
    }

    private function validateMedia(string $fullContent, array $parsedMedia): array {
        $media = $parsedMedia;

        preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $fullContent, $imgMatches);
        if (empty($imgMatches[1]) === false) {
            foreach ($imgMatches[1] as $imgUrl) {
                if ($this->mediaHelper->isImageUrl($imgUrl) === true) {
                    $caption = $this->extractImageCaption($fullContent, $imgUrl);
                    $media[] = [
                        'url'     => $imgUrl,
                        'caption' => $caption,
                    ];
                }
            }
        }

        preg_match_all('/<video[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $fullContent, $videoMatches);
        if (empty($videoMatches[1]) === false) {
            foreach ($videoMatches[1] as $videoUrl) {
                if ($this->mediaHelper->isVideoUrl($videoUrl) === true) {
                    $media[] = [
                        'url'     => $videoUrl,
                        'caption' => null,
                    ];
                }
            }
        }

        $uniqueMedia = [];
        $seenUrls    = [];
        foreach ($media as $item) {
            if ( ! empty($item['url']) && in_array($item['url'], $seenUrls, true) === false) {
                $uniqueMedia[] = $item;
                $seenUrls[]    = $item['url'];
            }
        }

        return $uniqueMedia;
    }

    private function extractImageCaption(string $content, string $imageUrl): ?string {
        if (preg_match('/<img[^>]+src=["\']' . preg_quote($imageUrl, '/') . '["\'][^>]*alt=["\']([^"\']+)["\'][^>]*>/i', $content, $matches)) {
            return trim($matches[1]);
        }

        if (preg_match('/<img[^>]+src=["\']' . preg_quote($imageUrl, '/') . '["\'][^>]*title=["\']([^"\']+)["\'][^>]*>/i', $content, $matches)) {
            return trim($matches[1]);
        }

        if (preg_match('/<img[^>]+src=["\']' . preg_quote($imageUrl, '/') . '["\'][^>]*>.*?<figcaption[^>]*>([^<]+)<\/figcaption>/is', $content, $matches)) {
            return trim(strip_tags($matches[1]));
        }

        return null;
    }
}
