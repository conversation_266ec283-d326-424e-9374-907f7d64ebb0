<?php

declare(strict_types=1);

namespace App\Modules\ParserAdder\Services;

use App\Helpers\MediaHelper;
use App\Models\Media;
use App\Repositories\MediaRepository;
use App\Repositories\PublisherEndpointRepository;
use App\Repositories\ThumbnailRepository;

class CreatesArticleMedia {
    public function __construct(
        private MediaRepository $mediaRepository,
        private PublisherEndpointRepository $publisherEndpointsRepository,
        private ThumbnailRepository $thumbnailRepository,
        private RetrievesImageProxyLinks $retrievesImageProxyLinks,
        private MediaHelper $mediaHelper,
    ) {
    }

    public function execute(array $coverImage, array $mediaArray, int $publisherId, int $channelId): array {
        $hasProxyImage = $this->publisherEndpointsRepository->getHasProxyImage($publisherId, $channelId);

        $mediaIds      = [];
        $processedUrls = [];

        if (empty($coverImage) === false && empty($coverImage['url']) === false) {
            $coverMediaId = $this->handleMedia($coverImage, $hasProxyImage, $processedUrls);
            if ($coverMediaId !== null) {
                $mediaIds[] = $coverMediaId;
            }
        }

        foreach ($mediaArray as $media) {
            $mediaId = $this->handleMedia($media, $hasProxyImage, $processedUrls);
            if ($mediaId !== null) {
                $mediaIds[] = $mediaId;
            }
        }

        return $mediaIds;
    }

    private function handleMedia(array $media, bool $hasProxyImage, array &$processedUrls): ?int {
        $originalUrl = $media['url'] ?? '';
        if (empty($originalUrl) === true || in_array($originalUrl, $processedUrls, true)) {
            return null;
        }

        $mediaModel = $this->insertMedia($media, $hasProxyImage);
        if ($mediaModel !== null) {
            $this->insertThumbnail($mediaModel, $hasProxyImage);
            $processedUrls[] = $originalUrl;

            return $mediaModel->id;
        }

        return null;
    }

    private function insertThumbnail(Media $media, bool $hasProxyImage): void {
        $imageUrls = [
            'square' => $media->url,
            'wide'   => $media->url,
        ];

        if ($hasProxyImage === true) {
            $imageUrls = $this->retrievesImageProxyLinks->execute(true, $media->url);
        }

        if ($this->mediaHelper->isMediaUrlValid($imageUrls['square']) === false || $this->mediaHelper->isMediaUrlValid($imageUrls['wide']) === false) {
            return;
        }

        $this->thumbnailRepository->create([
            'squareUrl'          => $imageUrls['square'],
            'wideUrl'            => $imageUrls['wide'],
            'squareImageSize'    => config('image_proxy.square_size'),
            'wideImageWidth'     => config('image_proxy.wide_width'),
            'wideImageHeight'    => config('image_proxy.wide_height'),
            'mediaOwnerType'     => 'media',
            'mediaOwnerId'       => $media->id,
        ]);
    }

    private function insertMedia(array $media, bool $hasProxyImage): ?Media {

        if ($hasProxyImage === true) {
            $media['url'] = $this->retrievesImageProxyLinks->execute(false, $media['url'])['regular'];
        }

        $existingMedia = $this->mediaRepository->findWhere(['url' => $media['url']]);
        if ($existingMedia !== null) {
            return $existingMedia;
        }

        if ($this->mediaHelper->isMediaUrlValid($media['url']) === false) {
            return null;
        }

        $width  = 0;
        $height = 0;

        if ($this->mediaHelper->isVideoUrl($media['url']) === false) {
            $mediaSize = $this->mediaHelper->getImageSize($media['url']);
            $width     = $mediaSize['width'];
            $height    = $mediaSize['height'];
        }

        return $this->mediaRepository->create([
            'caption'     => $media['caption'] ?? '',
            'url'         => $media['url'],
            'originalURL' => '',
            'wpURL'       => '',
            'imageWidth'  => $width ?? 0,
            'imageHeight' => $height ?? 0,
        ]);
    }
}
