<?php

declare(strict_types=1);

namespace App\Modules\ParserAdder\Logics;

use App\Classes\Constants\ServerParameters;
use App\Exceptions\InvalidAiResponseException;
use App\Exceptions\ParseArticleException;
use App\Helpers\ContentHelper;
use App\Modules\ParserAdder\Services\ParsesArticleContent;
use App\Repositories\PublisherCrawlerSettingRepository;
use App\Services\BypassCloudflareService;
use App\Services\GuzzleReadService;
use App\Services\HeadlessBrowserService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Throwable;

class ParseArticleDataManuallyLogic {
    public function __construct(
        private PublisherCrawlerSettingRepository $publisherCrawlerSettingRepository,
        private BypassCloudflareService $bypassCloudflareService,
        private HeadlessBrowserService $headlessBrowserService,
        private GuzzleReadService $guzzleReadService,
        private ContentHelper $contentHelper,
        private ParsesArticleContent $parsesArticleContent,
    ) {
    }

    public function execute(Request $request): array {
        try {
            $publisherId                   = $request->input('publisher_id');
            $channelId                     = $request->input('channel_id');
            $articleDataRssItem            = $request->input('article_data_rss_item');
            $articleDataHtmlLink           = $request->input('article_data_html_link');
            $useAiParsing                  = $request->input('use_ai_parsing');
            $crawlerSetting                = $this->publisherCrawlerSettingRepository->findWhere(['publisher_id' => $publisherId, 'channel_id' => $channelId]);

            if ($crawlerSetting === null) {
                throw new ParseArticleException(ServerParameters::HTTP_STATUS_INTERNAL_SERVER_ERROR, 'Crawler setting not found');
            }

            $customPrompt                  = $crawlerSetting->custom_prompt ?? '';
            $useBypassCloudflare           = $crawlerSetting->to_puppeteer;
            $useHeadlessBrowser            = $crawlerSetting->use_headless_browser;

            $rawArticleData      = $articleDataRssItem;
            if ($articleDataHtmlLink !== null) {
                if ($useHeadlessBrowser === true) {
                    $rawContentObject = $this->headlessBrowserService->getRawHtmlOrRss($articleDataHtmlLink);
                } elseif ($useBypassCloudflare === true) {
                    $rawContentObject = $this->bypassCloudflareService->getRawHtmlOrRss($articleDataHtmlLink);
                } else {
                    $rawContentObject = $this->guzzleReadService->getRawHtmlOrRss($articleDataHtmlLink);
                }

                if ($rawContentObject === null) {
                    Log::info("Skipping article due to timeout or error for URL: {$articleDataHtmlLink}");

                    return [];
                }

                $articleContent = $rawContentObject->getRawContent();
                $rawArticleData = $this->contentHelper->cleanRawData($articleContent);
            }

            return $this->parsesArticleContent->execute(
                $rawArticleData,
                $customPrompt,
                $useAiParsing,
                $articleDataHtmlLink !== null,
                $publisherId
            );
        } catch (InvalidAiResponseException $exception) {
            report($exception);

            return [];
        } catch (Throwable $exception) {
            throw new ParseArticleException(ServerParameters::HTTP_STATUS_INTERNAL_SERVER_ERROR, $exception->getMessage());
        }
    }
}
