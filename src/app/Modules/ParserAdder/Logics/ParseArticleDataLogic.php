<?php

declare(strict_types=1);

namespace App\Modules\ParserAdder\Logics;

use App\Classes\Constants\ServerParameters;
use App\Classes\ValueObjects\ArticleProcessingDataObject;
use App\Exceptions\InvalidAiResponseException;
use App\Exceptions\ParseArticleException;
use App\Helpers\ContentHelper;
use App\Modules\ParserAdder\Services\ParsesArticleContent;
use App\Services\BypassCloudflareService;
use App\Services\GuzzleReadService;
use App\Services\HeadlessBrowserService;
use Illuminate\Support\Facades\Log;
use Throwable;

class ParseArticleDataLogic {
    public function __construct(
        private BypassCloudflareService $bypassCloudflareService,
        private HeadlessBrowserService $headlessBrowserService,
        private GuzzleReadService $guzzleReadService,
        private ContentHelper $contentHelper,
        private ParsesArticleContent $parsesArticleContent,
    ) {
    }

    public function execute(ArticleProcessingDataObject $articleProcessingDataObject): array {
        try {
            $publisherId         = $articleProcessingDataObject->getPublisherId();
            $articleDataRssItem  = $articleProcessingDataObject->getArticleDataRssItem();
            $articleDataHtmlLink = $articleProcessingDataObject->getArticleDataHtmlLink();
            $customPrompt        = $articleProcessingDataObject->getCustomPrompt();
            $useBypassCloudflare = $articleProcessingDataObject->useBypassCloudflare();
            $useHeadlessBrowser  = $articleProcessingDataObject->useHeadlessBrowser();
            $useAiParsing        = $articleProcessingDataObject->useAiParsing();

            if ($articleDataRssItem === null && $articleDataHtmlLink === null) {
                throw new ParseArticleException(ServerParameters::HTTP_STATUS_BAD_REQUEST, 'Both $articleDataRssItem and $articleDataHtmlLink are NULL');
            }

            $rawArticleData      = $articleDataRssItem;
            if ($articleDataHtmlLink !== null) {
                if ($useHeadlessBrowser === true) {
                    $rawContentObject = $this->headlessBrowserService->getRawHtmlOrRss($articleDataHtmlLink);
                } elseif ($useBypassCloudflare === true) {
                    $rawContentObject = $this->bypassCloudflareService->getRawHtmlOrRss($articleDataHtmlLink);
                } else {
                    $rawContentObject = $this->guzzleReadService->getRawHtmlOrRss($articleDataHtmlLink);
                }

                if ($rawContentObject === null) {
                    Log::info("Skipping article due to timeout or error for URL: {$articleDataHtmlLink}");

                    return [];
                }

                $articleContent = $rawContentObject->getRawContent();
                $rawArticleData = $this->contentHelper->cleanRawData($articleContent);
            }

            return $this->parsesArticleContent->execute(
                $rawArticleData,
                $customPrompt,
                $useAiParsing,
                $articleDataHtmlLink !== null,
                $publisherId,

            );
        } catch (InvalidAiResponseException $exception) {
            report($exception);

            return [];
        } catch (Throwable $exception) {
            throw new ParseArticleException(ServerParameters::HTTP_STATUS_INTERNAL_SERVER_ERROR, $exception->getMessage());
        }
    }
}
