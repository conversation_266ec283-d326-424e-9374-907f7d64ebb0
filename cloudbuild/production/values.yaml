#DEPLOYMENT
fullnameOverride: newswav-smart-crawler
nameOverride: newswav-smart-crawler
replicaCount: 2
tier: backend
namespace: newswav

useDeploy: false

image:
  repository: asia-southeast1-docker.pkg.dev/bustling-sunset-220007/newswav-smart-crawler/newswav-smart-crawler
  tag: ""

resources:
  limits:
    memory: 1000Mi
    cpu: "600m"
  requests:
    memory: 500Mi
    cpu: "150m"

envFrom:
  - configMapRef:
      name: newswav-smart-crawler-config
  - secretRef:
      name: newswav-smart-crawler-gcpsm

#CONFIGMAP
configmap:
  enabled: true
  configmaps:
    - name: newswav-smart-crawler-config
      data:
        APP_NAME: "newswav-smart-crawler"
        APP_ENV: "production"
        APP_DEBUG: "false"
        APP_URL: https://newswav-smart-crawler.newswav.com

        LOG_CHANNEL: "stack"
        LOG_LEVEL: "debug"

        DB_CONNECTION: "mysql"
        DB_HOST: "*************"
        DB_HOST_REPLICA: "*************"
        DB_HOST_REPLICA_2: "*************"
        DB_PORT: "3306"
        DB_DATABASE: "newswav_news"

        BYPASSCF_SERVICE_URL: https://bypass-cf-service-418988694021.asia-southeast1.run.app/scrape
        HEADLESS_BROWSER_SERVICE_URL: https://playwright-scraper-418988694021.asia-southeast1.run.app/scrape-fast
        BYPASSCF_TIMEOUT: "30"
        HEADLESS_BROWSER_TIMEOUT: "30"
        BYPASSCF_RETRY_ATTEMPTS: "3"
        GOOGLE_CLOUD_PROJECT_ID: "bustling-sunset-220007"
        GEMINI_LOCATION: "global"
        PARSER_ADDER_SERVICE_BASE_URL: "https://newswav-smart-crawler-418988694021.asia-southeast1.run.app"
        OUTBOX_BASE_URL: "http://**********:80/outbox/api/event"
        OUTBOX_HEADER_KEY: "X-NW-OUTBOX"

        QUEUE_CONNECTION: "redis"

        REDIS_HOST: "**********"
        REDIS_DB: "2"

        SENTRY_LARAVEL_DSN: "https://<EMAIL>/4509632027557968"


cronjob:
  enabled: true
  fail_limit: 3
  success_limit: 1
  jobs:
    - name: smart-fetcher-w1
      command: /var/www/html/artisan fetch-and-process-articles-by-worker w1
      schedule: "*/4 * * * *"
      backoff_limit: 1

#    - name: smart-fetcher-w2
#      command: /var/www/html/artisan fetch-and-process-articles-by-worker w2
#      schedule: "*/4 * * * *"
#      backoff_limit: 1
#
#    - name: smart-fetcher-w3
#      command: /var/www/html/artisan fetch-and-process-articles-by-worker w3
#      schedule: "*/4 * * * *"
#      backoff_limit: 1
#
#    - name: smart-fetcher-w4
#      command: /var/www/html/artisan fetch-and-process-articles-by-worker w4
#      schedule: "*/4 * * * *"
#      backoff_limit: 1
#
#    - name: smart-fetcher-w5
#      command: /var/www/html/artisan fetch-and-process-articles-by-worker w5
#      schedule: "*/4 * * * *"
#      backoff_limit: 1
#
#    - name: smart-fetcher-w6
#      command: /var/www/html/artisan fetch-and-process-articles-by-worker w6
#      schedule: "*/4 * * * *"
#      backoff_limit: 1

serviceAccount:
  create: true
  annotations:
        iam.gke.io/gcp-service-account: <EMAIL>
  name: newswav-smart-crawler-sa
